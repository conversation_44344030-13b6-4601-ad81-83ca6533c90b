import pandas as pd
import numpy as np
from sklearn.preprocessing import RobustScaler, LabelEncoder
from datetime import datetime

def criar_features_avancadas(df):
    """Cria features mais sofisticadas para melhorar o modelo"""
    
    # 1. FEATURES TEMPORAIS AVANÇADAS
    df['data_vencimento'] = pd.to_datetime(df['data_vencimento'])
    df['data_pagamento'] = pd.to_datetime(df['data_pagamento'])
    
    # Dia da semana do vencimento (comportamento sazonal)
    df['dia_semana_vencimento'] = df['data_vencimento'].dt.dayofweek
    df['fim_semana_vencimento'] = (df['dia_semana_vencimento'] >= 5).astype(int)
    
    # Mês do vencimento (sazonalidade anual)
    df['mes_vencimento'] = df['data_vencimento'].dt.month
    df['trimestre_vencimento'] = df['data_vencimento'].dt.quarter
    
    # 2. FEATURES FINANCEIRAS SOFISTICADAS
    # Razão valor pago / valor boleto
    df['razao_pagamento'] = df['valor_pago'] / (df['valor boleto'] + 1e-8)
    
    # Diferença absoluta e percentual
    df['diferenca_valor'] = df['valor_pago'] - df['valor boleto']
    df['diferenca_percentual'] = df['diferenca_valor'] / (df['valor boleto'] + 1e-8)
    
    # Categorização de valores
    df['faixa_valor'] = pd.cut(df['valor boleto'], 
                            bins=[0, 100, 500, 1000, 5000, np.inf],
                            labels=['muito_baixo', 'baixo', 'medio', 'alto', 'muito_alto'])
    
    # 3. FEATURES COMPORTAMENTAIS
    # Histórico por pagador
    df['historico_atrasos'] = df.groupby('pagador_documento')['dias_de_atraso'].transform('mean')
    df['desvio_atraso_pagador'] = df.groupby('pagador_documento')['dias_de_atraso'].transform('std').fillna(0)
    df['count_boletos_pagador'] = df.groupby('pagador_documento').cumcount() + 1
    
    # Padrão de acesso
    df['acesso_por_valor'] = df['qtde_acessado_pagador'] / (df['valor boleto'] + 1e-8)
    df['alta_interacao'] = (df['qtde_acessado_pagador'] > df['qtde_acessado_pagador'].quantile(0.75)).astype(int)
    
    # 4. FEATURES GEOGRÁFICAS
    # Agrupamento por região
    df['regiao_risco'] = df.groupby('pagador_cidade')['dias_de_atraso'].transform('mean')
    df['cidade_volume'] = df.groupby('pagador_cidade').cumcount() + 1
    
    # 5. FEATURES DE INTERAÇÃO
    df['valor_x_acesso'] = df['valor boleto'] * df['qtde_acessado_pagador']
    df['valor_x_historico'] = df['valor boleto'] * df['historico_atrasos']
    
    return df

# Aplicar feature engineering
dados_melhorados = criar_features_avancadas(dados_pagos_final.copy())