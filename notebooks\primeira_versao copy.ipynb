{"cells": [{"cell_type": "markdown", "id": "baf18269", "metadata": {}, "source": ["importar bibliotecas"]}, {"cell_type": "code", "execution_count": 1, "id": "d2c719d0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import glob\n", "from IPython.display import display\n", "from pathlib import Path\n", "import re"]}, {"cell_type": "markdown", "id": "f106d054", "metadata": {}, "source": ["juntar to<PERSON> as tabelas + criar coluna de grupo"]}, {"cell_type": "code", "execution_count": 2, "id": "2044f3ad", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "id_grupo", "rawType": "int64", "type": "integer"}, {"name": "id_beneficiario", "rawType": "int64", "type": "integer"}, {"name": "Numero_do_boleto", "rawType": "int64", "type": "integer"}, {"name": "data_inclusao", "rawType": "object", "type": "string"}, {"name": "status_boleto", "rawType": "object", "type": "string"}, {"name": "data_vencto", "rawType": "object", "type": "string"}, {"name": "vl_boleto", "rawType": "float64", "type": "float"}, {"name": "dt_pagto", "rawType": "object", "type": "string"}, {"name": "vl_pagto", "rawType": "object", "type": "string"}, {"name": "banco", "rawType": "object", "type": "string"}, {"name": "id_pagador", "rawType": "int64", "type": "integer"}, {"name": "pagador_cep", "rawType": "object", "type": "unknown"}, {"name": "pagador_cidade", "rawType": "object", "type": "string"}, {"name": "qtd_acessos_pagador", "rawType": "object", "type": "string"}, {"name": "pagador_dt_ultimo_acesso", "rawType": "object", "type": "string"}, {"name": "pagador_cnpjcpf", "rawType": "object", "type": "string"}, {"name": "pagador_inscricao_hash", "rawType": "object", "type": "string"}, {"name": "valor_abatimento", "rawType": "object", "type": "unknown"}, {"name": "tipo_juros", "rawType": "object", "type": "string"}, {"name": "juros", "rawType": "float64", "type": "float"}, {"name": "tipo_multa", "rawType": "object", "type": "string"}, {"name": "multa", "rawType": "object", "type": "unknown"}, {"name": "grupo", "rawType": "object", "type": "string"}], "ref": "cb9f79af-279c-48bb-b4b8-884c650a84b9", "rows": [["0", "41", "129", "475124351", "2025-06-24", "REGISTRADO", "2025-07-30", "360.0", "\\N", "\\N", "BANCO SANTANDER (BRASIL) S.A.", "19283518", "2809040", "SAO PAULO", "\\N", "\\N", "CPF", "99e02f3b1ca56d979ad615782a839450b7dbabb155911046386eb9d34ddaf125", "0.0", "M", "0.11", "P", "2.0", "GF"], ["1", "41", "79", "475937945", "2025-06-27", "REGISTRADO", "2025-07-23", "208.08", "\\N", "\\N", "BANCO SANTANDER (BRASIL) S.A.", "1420019", "8275710", "SAO PAULO", "\\N", "\\N", "CPF", "24d3131aeddadee962b8c6aa27c14e04ba5482743fb76f471819933b73c01a38", "0.0", "M", "0.06", "P", "2.0", "GF"], ["2", "41", "85", "476378993", "2025-06-30", "REGISTRADO", "2025-07-24", "297.02", "\\N", "\\N", "BANCO SANTANDER (BRASIL) S.A.", "4381443", "9175010", "SANTO ANDRE (SP", "\\N", "\\N", "CPF", "9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbbc3b7ba5bef574a2a04", "0.0", "M", "0.09", "P", "2.0", "GF"], ["3", "41", "85", "476379036", "2025-06-30", "REGISTRADO", "2025-07-29", "297.02", "\\N", "\\N", "BANCO SANTANDER (BRASIL) S.A.", "4130986", "9060180", "SANTO ANDRE (SP", "\\N", "\\N", "CPF", "9d75dbc691022c487a29db4bbbdd04abc705c31767eff0a10347fac58873801b", "0.0", "M", "0.09", "P", "2.0", "GF"], ["4", "41", "85", "476379099", "2025-06-30", "REGISTRADO", "2025-07-28", "260.55", "\\N", "\\N", "BANCO SANTANDER (BRASIL) S.A.", "4381441", "9132190", "SANTO ANDRE (SP", "\\N", "\\N", "CPF", "bfd5f75eb99f65b4c61617af3d2a4b4860a74f8e407ed8666571192072cc8fe0", "0.0", "M", "0.08", "P", "2.0", "GF"]], "shape": {"columns": 23, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id_grupo</th>\n", "      <th>id_beneficiario</th>\n", "      <th>Numero_do_boleto</th>\n", "      <th>data_inclusao</th>\n", "      <th>status_boleto</th>\n", "      <th>data_vencto</th>\n", "      <th>vl_boleto</th>\n", "      <th>dt_pagto</th>\n", "      <th>vl_pagto</th>\n", "      <th>banco</th>\n", "      <th>...</th>\n", "      <th>qtd_acessos_pagador</th>\n", "      <th>pagador_dt_ultimo_acesso</th>\n", "      <th>pagador_cnpjcpf</th>\n", "      <th>pagador_inscricao_hash</th>\n", "      <th>valor_abatimento</th>\n", "      <th>tipo_juros</th>\n", "      <th>juros</th>\n", "      <th>tipo_multa</th>\n", "      <th>multa</th>\n", "      <th>grupo</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>41</td>\n", "      <td>129</td>\n", "      <td>475124351</td>\n", "      <td>2025-06-24</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-30</td>\n", "      <td>360.00</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>...</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>CPF</td>\n", "      <td>99e02f3b1ca56d979ad615782a839450b7dbabb1559110...</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>0.11</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>41</td>\n", "      <td>79</td>\n", "      <td>475937945</td>\n", "      <td>2025-06-27</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-23</td>\n", "      <td>208.08</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>...</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>CPF</td>\n", "      <td>24d3131aeddadee962b8c6aa27c14e04ba5482743fb76f...</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>0.06</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>41</td>\n", "      <td>85</td>\n", "      <td>476378993</td>\n", "      <td>2025-06-30</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-24</td>\n", "      <td>297.02</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>...</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>CPF</td>\n", "      <td>9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbb...</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>0.09</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>41</td>\n", "      <td>85</td>\n", "      <td>476379036</td>\n", "      <td>2025-06-30</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-29</td>\n", "      <td>297.02</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>...</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>CPF</td>\n", "      <td>9d75dbc691022c487a29db4bbbdd04abc705c31767eff0...</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>0.09</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>41</td>\n", "      <td>85</td>\n", "      <td>476379099</td>\n", "      <td>2025-06-30</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-28</td>\n", "      <td>260.55</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>...</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>CPF</td>\n", "      <td>bfd5f75eb99f65b4c61617af3d2a4b4860a74f8e407ed8...</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>0.08</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 23 columns</p>\n", "</div>"], "text/plain": ["   id_grupo  id_beneficiario  Numero_do_boleto data_inclusao status_boleto  \\\n", "0        41              129         475124351    2025-06-24    REGISTRADO   \n", "1        41               79         475937945    2025-06-27    REGISTRADO   \n", "2        41               85         476378993    2025-06-30    REGISTRADO   \n", "3        41               85         476379036    2025-06-30    REGISTRADO   \n", "4        41               85         476379099    2025-06-30    REGISTRADO   \n", "\n", "  data_vencto  vl_boleto dt_pagto vl_pagto                          banco  \\\n", "0  2025-07-30     360.00       \\N       \\N  BANCO SANTANDER (BRASIL) S.A.   \n", "1  2025-07-23     208.08       \\N       \\N  BANCO SANTANDER (BRASIL) S.A.   \n", "2  2025-07-24     297.02       \\N       \\N  BANCO SANTANDER (BRASIL) S.A.   \n", "3  2025-07-29     297.02       \\N       \\N  BANCO SANTANDER (BRASIL) S.A.   \n", "4  2025-07-28     260.55       \\N       \\N  BANCO SANTANDER (BRASIL) S.A.   \n", "\n", "   ...  qtd_acessos_pagador pagador_dt_ultimo_acesso pagador_cnpjcpf  \\\n", "0  ...                   \\N                       \\N             CPF   \n", "1  ...                   \\N                       \\N             CPF   \n", "2  ...                   \\N                       \\N             CPF   \n", "3  ...                   \\N                       \\N             CPF   \n", "4  ...                   \\N                       \\N             CPF   \n", "\n", "                              pagador_inscricao_hash valor_abatimento  \\\n", "0  99e02f3b1ca56d979ad615782a839450b7dbabb1559110...              0.0   \n", "1  24d3131aeddadee962b8c6aa27c14e04ba5482743fb76f...              0.0   \n", "2  9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbb...              0.0   \n", "3  9d75dbc691022c487a29db4bbbdd04abc705c31767eff0...              0.0   \n", "4  bfd5f75eb99f65b4c61617af3d2a4b4860a74f8e407ed8...              0.0   \n", "\n", "  tipo_juros juros tipo_multa multa  grupo  \n", "0          M  0.11          P   2.0     GF  \n", "1          M  0.06          P   2.0     GF  \n", "2          M  0.09          P   2.0     GF  \n", "3          M  0.09          P   2.0     GF  \n", "4          M  0.08          P   2.0     GF  \n", "\n", "[5 rows x 23 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["(1635429, 23)\n"]}], "source": ["arquivos = glob.glob(\"../dados/*.csv\") # lista de caminhos para todos os arquivos .csv dentro da pasta ../dados/\n", "dfs = [] # cria uma lista vazia\n", "for arq in arquivos: # para cada caminho dentro de todos os caminhos para as tabelas dos dados:\n", "    df = pd.read_csv(arq, sep=\"\\t\", encoding=\"utf-8-sig\", engine=\"python\") # leitura do arquivo\n", "    nome_arquivo = Path(arq).name  # Ex: \"Grupo com registro entre 07-2024 a 06-2025- GW.csv\"\n", "    grupo = re.search(r\"- ([^-\\s]+)\\.csv$\", nome_arquivo, re.IGNORECASE)\n", "    sigla_grupo = grupo.group(1) if grupo else \"Desconhecido\"\n", "    df[\"grupo\"] = sigla_grupo\n", "    dfs.append(df) # adiciona cada df a lista de dfs\n", "dados = pd.concat(dfs, ignore_index=True) # concatena todos os dataframes da lista dfs em um único dataframe chamado dt\n", "display(dados.head()) # mostra as primeiras 5 linhas\n", "print(dados.shape) # imprime o numero de linhas e colunas do dt"]}, {"cell_type": "markdown", "id": "8cad87a5", "metadata": {}, "source": ["saber o nome das colunas"]}, {"cell_type": "code", "execution_count": 3, "id": "c8fbe9a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['id_grupo', 'id_beneficiario', 'Numero_do_boleto', 'data_inclusao', 'status_boleto', 'data_vencto', 'vl_boleto', 'dt_pagto', 'vl_pagto', 'banco', 'id_pagador', 'pagador_cep', 'pagador_cidade', 'qtd_acessos_pagador', 'pagador_dt_ultimo_acesso', 'pagador_cnpjcpf', 'pagador_inscricao_hash', 'valor_abatimento', 'tipo_juros', 'juros', 'tipo_multa', 'multa', 'grupo']\n"]}], "source": ["print(dados.columns.tolist())"]}, {"cell_type": "markdown", "id": "32673b1f", "metadata": {}, "source": ["cep para estado"]}, {"cell_type": "code", "execution_count": 4, "id": "c1415525", "metadata": {}, "outputs": [], "source": ["def cep_para_estado(cep):\n", "    if pd.isna(cep) or str(cep).strip() in ['\\\\N', 'nan', 'NÃO INFORMADO', 'NAO INFORMADO', 'NINF', 'nf', '']:\n", "        return None\n", "\n", "    # Converter para string e limpar\n", "    cep_str = str(cep).strip().replace('-', '').replace('.', '').replace(' ', '')\n", "\n", "    # <PERSON><PERSON> só dígitos\n", "    cep_str = ''.join([c for c in cep_str if c.isdigit()])\n", "\n", "    # Se tiver menos de 5 dígitos, inválido\n", "    if len(cep_str) < 5:\n", "        return None\n", "\n", "    # Completar com zeros à esquerda até 8 dígitos\n", "    cep_str = cep_str.zfill(8)  # '2809040' → '02809040'\n", "\n", "    # Pegar os 2 primeiros dígitos\n", "    dois_digitos = cep_str[:2]\n", "\n", "    # Mapeamento por 2 dígitos (como antes)\n", "    mapeamento = {\n", "        '01': 'SP', '02': 'SP', '03': 'SP', '04': 'SP', '05': 'SP', '06': 'SP', '07': 'SP', '08': 'SP', '09': 'SP',\n", "        '10': 'SP', '11': 'SP', '12': 'SP', '13': 'SP', '14': 'SP', '15': 'SP', '16': 'SP', '17': 'SP', '18': 'SP', '19': 'SP',\n", "        '20': 'R<PERSON>', '21': '<PERSON><PERSON>', '22': '<PERSON><PERSON>', '23': 'R<PERSON>', '24': '<PERSON><PERSON>', '25': 'R<PERSON>', '26': '<PERSON><PERSON>', '27': 'R<PERSON>', '28': '<PERSON><PERSON>',\n", "        '29': 'ES',\n", "        '30': 'MG', '31': 'MG', '32': 'MG', '33': 'MG', '34': 'MG', '35': 'MG', '36': 'MG', '37': 'MG', '38': 'MG', '39': 'MG',\n", "        '40': 'BA', '41': 'BA', '42': 'BA', '43': 'BA', '44': 'BA', '45': 'BA', '46': 'BA', '47': 'BA', '48': 'SE', '49': 'SE',\n", "        '50': 'PE', '51': 'PE', '52': 'PE', '53': 'PE', '54': 'PE', '55': 'PE', '56': 'PE', '57': 'AL', '58': 'PB', '59': 'RN',\n", "        '60': 'CE', '61': 'CE', '62': 'CE', '63': 'CE', '64': 'PI', '65': 'MA', '66': 'PA', '67': 'PA', '68': 'AP', '69': 'AM',\n", "        '70': 'DF', '71': 'DF', '72': 'DF', '73': 'GO', '74': 'GO', '75': 'GO', '76': 'TO', '77': 'TO', '78': 'MT', '79': 'MS',\n", "        '80': 'PR', '81': 'PR', '82': 'PR', '83': 'PR', '84': 'PR', '85': 'PR', '86': 'PR', '87': 'PR', '88': 'SC', '89': 'SC',\n", "        '90': 'RS', '91': 'RS', '92': 'RS', '93': 'RS', '94': 'RS', '95': 'RS', '96': 'RS', '97': 'RS', '98': 'RS', '99': 'RS'\n", "    }\n", "\n", "    return mapeamento.get(dois_digitos, None)"]}, {"cell_type": "code", "execution_count": 5, "id": "94ce6f2b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pagador_estado\n", "SP                      510871\n", "RS                      143526\n", "SC                      119880\n", "MG                      104946\n", "RJ                      100369\n", "PB                       82949\n", "PR                       80011\n", "BA                       47196\n", "PE                       42957\n", "MT                       39603\n", "CE                       39018\n", "GO                       38780\n", "AL                       35962\n", "TO                       32592\n", "ES                       32005\n", "AP                       29535\n", "AM                       25711\n", "MA                       22089\n", "RN                       21465\n", "DF                       20781\n", "SE                       20662\n", "MS                       18180\n", "PI                       14093\n", "PA                       11843\n", "Estado não informado       405\n", "Name: count, dtype: int64\n"]}], "source": ["# Aplicar\n", "dados['pagador_estado'] = dados['pagador_cep'].apply(cep_para_estado)\n", "\n", "# Substituir os valores None/NaN por \"Estado não informado\"\n", "dados['pagador_estado'] = dados['pagador_estado'].fillna('Estado não informado')\n", "\n", "# Verifique o resultado\n", "print(dados['pagador_estado'].value_counts(dropna=False))"]}, {"cell_type": "code", "execution_count": 6, "id": "e49e8a6a", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "data_inclusao", "rawType": "object", "type": "string"}, {"name": "status_boleto", "rawType": "object", "type": "string"}, {"name": "data_vencto", "rawType": "object", "type": "string"}, {"name": "vl_boleto", "rawType": "float64", "type": "float"}, {"name": "dt_pagto", "rawType": "object", "type": "string"}, {"name": "vl_pagto", "rawType": "object", "type": "string"}, {"name": "banco", "rawType": "object", "type": "string"}, {"name": "qtd_acessos_pagador", "rawType": "object", "type": "string"}, {"name": "pagador_cnpjcpf", "rawType": "object", "type": "string"}, {"name": "pagador_inscricao_hash", "rawType": "object", "type": "string"}, {"name": "tipo_juros", "rawType": "object", "type": "string"}, {"name": "juros", "rawType": "float64", "type": "float"}, {"name": "tipo_multa", "rawType": "object", "type": "string"}, {"name": "multa", "rawType": "object", "type": "unknown"}, {"name": "grupo", "rawType": "object", "type": "string"}, {"name": "pagador_estado", "rawType": "object", "type": "string"}], "ref": "7706cc0b-7f3f-4497-b77f-df17572cf0c0", "rows": [["0", "2025-06-24", "REGISTRADO", "2025-07-30", "360.0", "\\N", "\\N", "BANCO SANTANDER (BRASIL) S.A.", "\\N", "CPF", "99e02f3b1ca56d979ad615782a839450b7dbabb155911046386eb9d34ddaf125", "M", "0.11", "P", "2.0", "GF", "SP"], ["1", "2025-06-27", "REGISTRADO", "2025-07-23", "208.08", "\\N", "\\N", "BANCO SANTANDER (BRASIL) S.A.", "\\N", "CPF", "24d3131aeddadee962b8c6aa27c14e04ba5482743fb76f471819933b73c01a38", "M", "0.06", "P", "2.0", "GF", "SP"], ["2", "2025-06-30", "REGISTRADO", "2025-07-24", "297.02", "\\N", "\\N", "BANCO SANTANDER (BRASIL) S.A.", "\\N", "CPF", "9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbbc3b7ba5bef574a2a04", "M", "0.09", "P", "2.0", "GF", "SP"], ["3", "2025-06-30", "REGISTRADO", "2025-07-29", "297.02", "\\N", "\\N", "BANCO SANTANDER (BRASIL) S.A.", "\\N", "CPF", "9d75dbc691022c487a29db4bbbdd04abc705c31767eff0a10347fac58873801b", "M", "0.09", "P", "2.0", "GF", "SP"], ["4", "2025-06-30", "REGISTRADO", "2025-07-28", "260.55", "\\N", "\\N", "BANCO SANTANDER (BRASIL) S.A.", "\\N", "CPF", "bfd5f75eb99f65b4c61617af3d2a4b4860a74f8e407ed8666571192072cc8fe0", "M", "0.08", "P", "2.0", "GF", "SP"]], "shape": {"columns": 16, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>data_inclusao</th>\n", "      <th>status_boleto</th>\n", "      <th>data_vencto</th>\n", "      <th>vl_boleto</th>\n", "      <th>dt_pagto</th>\n", "      <th>vl_pagto</th>\n", "      <th>banco</th>\n", "      <th>qtd_acessos_pagador</th>\n", "      <th>pagador_cnpjcpf</th>\n", "      <th>pagador_inscricao_hash</th>\n", "      <th>tipo_juros</th>\n", "      <th>juros</th>\n", "      <th>tipo_multa</th>\n", "      <th>multa</th>\n", "      <th>grupo</th>\n", "      <th>pagador_estado</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-06-24</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-30</td>\n", "      <td>360.00</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>\\N</td>\n", "      <td>CPF</td>\n", "      <td>99e02f3b1ca56d979ad615782a839450b7dbabb1559110...</td>\n", "      <td>M</td>\n", "      <td>0.11</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-06-27</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-23</td>\n", "      <td>208.08</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>\\N</td>\n", "      <td>CPF</td>\n", "      <td>24d3131aeddadee962b8c6aa27c14e04ba5482743fb76f...</td>\n", "      <td>M</td>\n", "      <td>0.06</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-06-30</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-24</td>\n", "      <td>297.02</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>\\N</td>\n", "      <td>CPF</td>\n", "      <td>9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbb...</td>\n", "      <td>M</td>\n", "      <td>0.09</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-06-30</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-29</td>\n", "      <td>297.02</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>\\N</td>\n", "      <td>CPF</td>\n", "      <td>9d75dbc691022c487a29db4bbbdd04abc705c31767eff0...</td>\n", "      <td>M</td>\n", "      <td>0.09</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-06-30</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-28</td>\n", "      <td>260.55</td>\n", "      <td>\\N</td>\n", "      <td>\\N</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>\\N</td>\n", "      <td>CPF</td>\n", "      <td>bfd5f75eb99f65b4c61617af3d2a4b4860a74f8e407ed8...</td>\n", "      <td>M</td>\n", "      <td>0.08</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "      <td>SP</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  data_inclusao status_boleto data_vencto  vl_boleto dt_pagto vl_pagto  \\\n", "0    2025-06-24    REGISTRADO  2025-07-30     360.00       \\N       \\N   \n", "1    2025-06-27    REGISTRADO  2025-07-23     208.08       \\N       \\N   \n", "2    2025-06-30    REGISTRADO  2025-07-24     297.02       \\N       \\N   \n", "3    2025-06-30    REGISTRADO  2025-07-29     297.02       \\N       \\N   \n", "4    2025-06-30    REGISTRADO  2025-07-28     260.55       \\N       \\N   \n", "\n", "                           banco qtd_acessos_pagador pagador_cnpjcpf  \\\n", "0  BANCO SANTANDER (BRASIL) S.A.                  \\N             CPF   \n", "1  BANCO SANTANDER (BRASIL) S.A.                  \\N             CPF   \n", "2  BANCO SANTANDER (BRASIL) S.A.                  \\N             CPF   \n", "3  BANCO SANTANDER (BRASIL) S.A.                  \\N             CPF   \n", "4  BANCO SANTANDER (BRASIL) S.A.                  \\N             CPF   \n", "\n", "                              pagador_inscricao_hash tipo_juros  juros  \\\n", "0  99e02f3b1ca56d979ad615782a839450b7dbabb1559110...          M   0.11   \n", "1  24d3131aeddadee962b8c6aa27c14e04ba5482743fb76f...          M   0.06   \n", "2  9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbb...          M   0.09   \n", "3  9d75dbc691022c487a29db4bbbdd04abc705c31767eff0...          M   0.09   \n", "4  bfd5f75eb99f65b4c61617af3d2a4b4860a74f8e407ed8...          M   0.08   \n", "\n", "  tipo_multa multa grupo pagador_estado  \n", "0          P   2.0    GF             SP  \n", "1          P   2.0    GF             SP  \n", "2          P   2.0    GF             SP  \n", "3          P   2.0    GF             SP  \n", "4          P   2.0    GF             SP  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["colunas_para_remover = ['id_grupo', 'id_beneficiario', 'Numero_do_boleto', 'id_pagador', 'pagador_cidade', 'pagador_dt_ultimo_acesso', 'valor_abatimento', 'pagador_cep']\n", "colunas_existentes = [col for col in colunas_para_remover if col in dados.columns]\n", "dados = dados.drop(columns=colunas_existentes)\n", "dados.head()"]}, {"cell_type": "markdown", "id": "f51efc95", "metadata": {}, "source": ["\\N e NAN"]}, {"cell_type": "code", "execution_count": 7, "id": "98a07c5a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Valores faltantes por coluna:\n", "data_inclusao             0\n", "status_boleto             0\n", "data_vencto               0\n", "vl_boleto                 0\n", "dt_pagto                  0\n", "vl_pagto                  0\n", "banco                     0\n", "qtd_acessos_pagador       0\n", "pagador_cnpjcpf           0\n", "pagador_inscricao_hash    0\n", "tipo_juros                0\n", "juros                     0\n", "tipo_multa                0\n", "multa                     0\n", "grupo                     0\n", "pagador_estado            0\n", "dtype: int64\n", "\n", "Linhas completamente vazias: 0\n"]}], "source": ["# 1. Quantos missing values por coluna?\n", "print(\"Valores faltantes por coluna:\")\n", "print(dados.isnull().sum())\n", "\n", "# 3. Há alguma linha completamente vazia?\n", "print(f\"\\nLinhas completamente vazias: {dados.isnull().all(axis=1).sum()}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "8ded7cc3", "metadata": {}, "outputs": [], "source": ["# Substitui '\\N' por NaN em todo o DataFrame\n", "dados = dados.replace(r'^\\\\N$', np.nan, regex=True)"]}, {"cell_type": "code", "execution_count": 9, "id": "c64f0e36", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Valores faltantes por coluna:\n", "data_inclusao                  0\n", "status_boleto                  0\n", "data_vencto                    0\n", "vl_boleto                      0\n", "dt_pagto                  631784\n", "vl_pagto                  628936\n", "banco                          0\n", "qtd_acessos_pagador       866994\n", "pagador_cnpjcpf                0\n", "pagador_inscricao_hash         0\n", "tipo_juros                     0\n", "juros                          0\n", "tipo_multa                755323\n", "multa                     755323\n", "grupo                          0\n", "pagador_estado                 0\n", "dtype: int64\n"]}], "source": ["print(\"Valores faltantes por coluna:\")\n", "print(dados.isnull().sum())"]}, {"cell_type": "code", "execution_count": 10, "id": "901c9e1a", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "data_inclusao", "rawType": "object", "type": "string"}, {"name": "status_boleto", "rawType": "object", "type": "string"}, {"name": "data_vencto", "rawType": "object", "type": "string"}, {"name": "vl_boleto", "rawType": "float64", "type": "float"}, {"name": "dt_pagto", "rawType": "object", "type": "unknown"}, {"name": "vl_pagto", "rawType": "object", "type": "unknown"}, {"name": "banco", "rawType": "object", "type": "string"}, {"name": "qtd_acessos_pagador", "rawType": "object", "type": "unknown"}, {"name": "pagador_cnpjcpf", "rawType": "object", "type": "string"}, {"name": "pagador_inscricao_hash", "rawType": "object", "type": "string"}, {"name": "tipo_juros", "rawType": "object", "type": "string"}, {"name": "juros", "rawType": "float64", "type": "float"}, {"name": "tipo_multa", "rawType": "object", "type": "string"}, {"name": "multa", "rawType": "object", "type": "unknown"}, {"name": "grupo", "rawType": "object", "type": "string"}, {"name": "pagador_estado", "rawType": "object", "type": "string"}], "ref": "5dfa69e7-d2e6-4c13-9189-10821788b6a7", "rows": [["0", "2025-06-24", "REGISTRADO", "2025-07-30", "360.0", null, null, "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "99e02f3b1ca56d979ad615782a839450b7dbabb155911046386eb9d34ddaf125", "M", "0.11", "P", "2.0", "GF", "SP"], ["1", "2025-06-27", "REGISTRADO", "2025-07-23", "208.08", null, null, "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "24d3131aeddadee962b8c6aa27c14e04ba5482743fb76f471819933b73c01a38", "M", "0.06", "P", "2.0", "GF", "SP"], ["2", "2025-06-30", "REGISTRADO", "2025-07-24", "297.02", null, null, "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbbc3b7ba5bef574a2a04", "M", "0.09", "P", "2.0", "GF", "SP"], ["3", "2025-06-30", "REGISTRADO", "2025-07-29", "297.02", null, null, "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "9d75dbc691022c487a29db4bbbdd04abc705c31767eff0a10347fac58873801b", "M", "0.09", "P", "2.0", "GF", "SP"], ["4", "2025-06-30", "REGISTRADO", "2025-07-28", "260.55", null, null, "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "bfd5f75eb99f65b4c61617af3d2a4b4860a74f8e407ed8666571192072cc8fe0", "M", "0.08", "P", "2.0", "GF", "SP"], ["5", "2025-06-30", "REGISTRADO", "2025-07-24", "283.21", null, null, "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "72cf28cb765b3e0933167d5d093e9228aad60505a881b100573660ab96af0a8f", "M", "0.08", "P", "2.0", "GF", "SP"], ["6", "2025-06-30", "REGISTRADO", "2025-07-24", "252.76", null, null, "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "3c73f998db00500cb505fa9a63a6fcca8b00554ce3832ce3d57301097f5d30e2", "M", "0.08", "P", "2.0", "GF", "SP"], ["7", "2025-06-30", "REGISTRADO", "2025-07-24", "352.9", null, null, "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "3e056afc1652b412b1a5cef2625563b8e4c7eb8e5334ef93434915b737572a00", "M", "0.11", "P", "2.0", "GF", "SP"], ["8", "2025-06-30", "REGISTRADO", "2025-07-24", "448.23", null, null, "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "271586e05b2b3f1f9a59232d9507ca15c5a6fcb7a5f14a426bc8c439be094190", "M", "0.13", "P", "2.0", "GF", "SP"], ["9", "2025-06-30", "REGISTRADO", "2025-07-23", "256.78", null, null, "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "5116a9e83abeb2920e1ec8c2cefcf92a0a20432b450363dfa910ff39388e9a1e", "M", "0.08", "P", "2.0", "GF", "SP"], ["10", "2025-06-30", "REGISTRADO", "2025-07-24", "333.29", null, null, "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "aad306f8bcf24dc6091280d238bc6918ab43f80f5b2a37a2d5d783cc912d7bcd", "M", "0.1", "P", "2.0", "GF", "SP"], ["11", "2024-07-01", "LIQUIDADO", "2024-07-22", "283.42", "2024-07-03", "263.58", "BANCO SANTANDER (BRASIL) S.A.", "2", "CPF", "f5e3d835f189bcdc3c06e012a54a648938e587ecf752a8cf12d26bf8f54b6c3a", "M", "0.08", "P", "2.0", "GF", "SP"], ["12", "2024-07-01", "LIQUIDADO", "2024-07-22", "330.42", "2024-07-03", "307.29", "BANCO SANTANDER (BRASIL) S.A.", "2", "CPF", "8cafe0ee5d35f8d717a9165a3a9cfd284dd080adc977cd42e18efd9b88032138", "M", "0.1", "P", "2.0", "GF", "SP"], ["13", "2024-07-03", "LIQUIDADO", "2024-07-22", "374.15", "2024-07-30", "382.39", "BANCO SANTANDER (BRASIL) S.A.", "4", "CPF", "fa65b15f453acb990f8492a5b30a58849d0c87985a0a90c500593ae50b02c677", "M", "0.11", "P", "2.0", "GF", "SP"], ["14", "2024-07-03", "LIQUIDADO", "2024-07-22", "324.13", "2024-07-16", "324.13", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "db63183e80b196093f7a06ee4affc35b89daa05849412f906dae012dc3ea481a", "M", "0.1", "P", "2.0", "GF", "SP"], ["15", "2024-07-03", "LIQUIDADO", "2024-07-22", "337.0", "2024-07-23", "337.00", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "a0b12a8471aec5c6605a529cf284990656af7385bc5e8567fe48b9350d2ecc1b", "M", "0.1", "P", "2.0", "GF", "SP"], ["16", "2024-07-03", "LIQUIDADO", "2024-07-22", "283.42", "2024-07-23", "283.42", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "74fae55a6c42fc13d907daadf3ee1b9ea0366dc057cfe3eb57a1c77c11b2aa9c", "M", "0.08", "P", "2.0", "GF", "SP"], ["17", "2024-07-03", "LIQUIDADO", "2024-07-22", "305.18", "2024-07-11", "305.18", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "eaf3129b8aa5efecfd193082fea0ce0d4e6ecfe86c89ca6507bcc261749ce826", "M", "0.09", "P", "2.0", "GF", "SP"], ["18", "2024-07-03", "LIQUIDADO", "2024-07-22", "325.52", "2024-07-10", "302.73", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "296737eff387c7d8cd675f2a8b8a2ec51cae0152c859d4023be1fb46b2016187", "M", "0.1", "P", "2.0", "GF", "SP"], ["19", "2024-07-03", "LIQUIDADO", "2024-07-22", "325.52", "2024-07-16", "325.52", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "c2868f8f45ee6970387115d89e745cc8eb5889ababa833b331e93ed54e3f5e08", "M", "0.1", "P", "2.0", "GF", "SP"], ["20", "2024-07-03", "LIQUIDADO", "2024-07-22", "305.17", "2024-07-08", "283.81", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "2ec824e754a6ecef074a824650ce8f4e7f367789f28670d483e10739870a69d6", "M", "0.09", "P", "2.0", "GF", "SP"], ["21", "2024-07-03", "LIQUIDADO", "2024-07-22", "327.92", "2024-07-08", "304.97", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "08a45924ac854b3d2c311f7f12995d4c273131cd2685eb95298d57f1476eb528", "M", "0.1", "P", "2.0", "GF", "SP"], ["22", "2024-07-03", "LIQUIDADO", "2024-07-22", "212.56", "2024-07-08", "197.68", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "6649b728134dbcd4295d0abc5e64c4e61322163bc09d919e37faf294904ab21b", "M", "0.06", "P", "2.0", "GF", "SP"], ["23", "2024-07-03", "LIQUIDADO", "2024-07-22", "325.52", "2024-07-16", "325.52", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "9f2c2df6c1d9a81666ebf02b2f0fa7a19bf28e793d5ad6106a09603484b7b937", "M", "0.1", "P", "2.0", "GF", "SP"], ["24", "2024-07-03", "LIQUIDADO", "2024-07-22", "337.0", "2024-07-18", "337.00", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "f8cff0e00872e9b3f567ca063988a6c19b76d050ec3045a853a96d8b854d6431", "M", "0.1", "P", "2.0", "GF", "SP"], ["25", "2024-07-03", "LIQUIDADO", "2024-07-22", "244.14", "2024-07-11", "227.05", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "309f0de52b7d17ddfbf5dfea92a77dcae81f463316e9f6b02031097d1bd1ec77", "M", "0.07", "P", "2.0", "GF", "SP"], ["26", "2024-07-03", "LIQUIDADO", "2024-07-22", "325.52", "2024-07-09", "302.73", "BANCO SANTANDER (BRASIL) S.A.", "2", "CPF", "1e9fc6dbc01f4c1105fa18f724863d4f51eacdaf311e38ef5d32eb95a4469407", "M", "0.1", "P", "2.0", "GF", "SP"], ["27", "2024-07-03", "LIQUIDADO", "2024-07-22", "325.52", "2024-07-11", "302.73", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "423cf279e4dc04e10986e82cc4973b2244542092c55ab684370046e0bd148887", "M", "0.1", "P", "2.0", "GF", "SP"], ["28", "2024-07-03", "LIQUIDADO", "2024-07-20", "283.42", "2024-07-08", "263.58", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "625f6dd1b70233b75bdc9143d338a86907c26fa2d31321b13fcfd5e85f3641e6", "M", "0.08", "P", "2.0", "GF", "SP"], ["29", "2024-07-03", "LIQUIDADO", "2024-07-22", "212.56", "2024-07-23", "212.56", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbbc3b7ba5bef574a2a04", "M", "0.06", "P", "2.0", "GF", "SP"], ["30", "2024-07-03", "LIQUIDADO", "2024-07-22", "264.66", "2024-07-11", "246.13", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "96beb1427803b97f37a165e6f2c98cc8d60b4c924c3b003f1fc9cc351cd12067", "M", "0.08", "P", "2.0", "GF", "SP"], ["31", "2024-07-03", "LIQUIDADO", "2024-07-22", "155.7", "2024-07-08", "144.80", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "1279aa233b3da7b6686e2ab21082a304e49742777b2f7415984d03711791b7ce", "M", "0.05", "P", "2.0", "GF", "SP"], ["32", "2024-07-03", "LIQUIDADO", "2024-07-22", "155.37", "2024-07-05", "144.49", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "b0d1325c1173bd3032d40f3d8fd727a46900620314515a6355570a7bbe1b9d10", "M", "0.05", "P", "2.0", "GF", "SP"], ["33", "2024-07-03", "LIQUIDADO", "2024-07-22", "155.37", "2024-07-16", "155.37", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "3c4d79de3360028aa3a8c5467e7f760bb09a31ced9c47e7648f0810597b0ec11", "M", "0.05", "P", "2.0", "GF", "SP"], ["34", "2024-07-03", "LIQUIDADO", "2024-07-22", "155.37", "2024-07-09", "144.49", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "5346efdff4f5e86e785953bcc9d9173edd4f1be13a556598a5c99301550bc96a", "M", "0.05", "P", "2.0", "GF", "SP"], ["35", "2024-07-03", "LIQUIDADO", "2024-07-22", "155.37", "2024-07-10", "144.49", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "bce1aef98d2ed4e4003dcfaf7784a35feb612bdb79f79f4d3d89d824c6f57ccc", "M", "0.05", "P", "2.0", "GF", "SP"], ["36", "2024-07-03", "LIQUIDADO", "2024-07-22", "155.37", "2024-07-17", "155.37", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "0832922c8e3584b858dcf8ff12894b8e8054c78cff5848ab5d4d106a7790a46f", "M", "0.05", "P", "2.0", "GF", "SP"], ["37", "2024-07-03", "LIQUIDADO", "2024-07-22", "293.45", "2024-07-05", "272.91", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "f673e845df1044043f739aacf5cb6f1531a6c0d827ecab3c417bf5205a358214", "M", "0.09", "P", "2.0", "GF", "SP"], ["38", "2024-07-03", "LIQUIDADO", "2024-07-22", "265.7", "2024-07-11", "247.10", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "de44b5871865677afc2b3b41dfbcb44841e93d5b512f8bd512ead46175406777", "M", "0.08", "P", "2.0", "GF", "SP"], ["39", "2024-07-03", "LIQUIDADO", "2024-07-22", "305.18", "2024-07-09", "283.82", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "15582f51a161603f38dc5c24f129ca2806808fd8618af671b97a0899301f2c6c", "M", "0.09", "P", "2.0", "GF", "SP"], ["40", "2024-07-03", "LIQUIDADO", "2024-07-22", "302.46", "2024-07-11", "281.29", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "163836a12f1e2848c553c21ab3fc4db703b9bb6ed0b71283475829076b8a65ee", "M", "0.09", "P", "2.0", "GF", "SP"], ["41", "2024-07-03", "LIQUIDADO", "2024-07-22", "325.52", "2024-07-08", "302.73", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "fd642efbc95cf5c98d7c9d8964b9fc47af166980e9c29dccc21ea1bd95670344", "M", "0.1", "P", "2.0", "GF", "SP"], ["42", "2024-07-03", "LIQUIDADO", "2024-07-22", "325.52", "2024-07-30", "332.72", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "b0f1bbc75f9ba96877bf541fc717e7884c36066833cdde8fe5c972b92c4799f7", "M", "0.1", "P", "2.0", "GF", "SP"], ["43", "2024-07-03", "LIQUIDADO", "2024-07-22", "325.52", "2024-07-09", "302.73", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "d0e197adbd494488def802f184c9148b5dd99346882e18ac2d415c9233c13e3c", "M", "0.1", "P", "2.0", "GF", "SP"], ["44", "2024-07-03", "LIQUIDADO", "2024-07-22", "355.61", "2024-07-11", "330.72", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "4c386425e17ccfba060818e28f9f006583eb993f65c48b1197ee6be02375b41d", "M", "0.11", "P", "2.0", "GF", "SP"], ["45", "2024-07-03", "LIQUIDADO", "2024-07-22", "283.42", "2024-07-16", "283.42", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "14eb4d9ccd43f7fb8e2d2d67228746536871d52ef442a1fb31f068f9037d9f0c", "M", "0.08", "P", "2.0", "GF", "SP"], ["46", "2024-07-03", "LIQUIDADO", "2024-07-22", "325.52", "2024-07-08", "302.73", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "aa3324888fc1d2abe01a6a9c32cdb8c7498072a6c497bab98bad0e034bc757f4", "M", "0.1", "P", "2.0", "GF", "SP"], ["47", "2024-07-03", "LIQUIDADO", "2024-07-22", "355.61", "2024-07-11", "330.72", "BANCO SANTANDER (BRASIL) S.A.", "2", "CPF", "ecdb43ad8633ba8622e99b79caa49f915c59c5ddbbc97d7fa03ffd98fea1f18d", "M", "0.11", "P", "2.0", "GF", "SP"], ["48", "2024-07-03", "LIQUIDADO", "2024-07-22", "374.35", "2024-07-23", "374.35", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "1613d8b0de42ccf90eff61ba5d5c01bfb5c137c802c656b2a278bae4b489c7fd", "M", "0.11", "P", "2.0", "GF", "SP"], ["49", "2024-07-03", "LIQUIDADO", "2024-07-22", "293.45", "2024-07-11", "272.91", "BANCO SANTANDER (BRASIL) S.A.", "0", "CPF", "eaf3129b8aa5efecfd193082fea0ce0d4e6ecfe86c89ca6507bcc261749ce826", "M", "0.09", "P", "2.0", "GF", "SP"]], "shape": {"columns": 16, "rows": 1635429}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>data_inclusao</th>\n", "      <th>status_boleto</th>\n", "      <th>data_vencto</th>\n", "      <th>vl_boleto</th>\n", "      <th>dt_pagto</th>\n", "      <th>vl_pagto</th>\n", "      <th>banco</th>\n", "      <th>qtd_acessos_pagador</th>\n", "      <th>pagador_cnpjcpf</th>\n", "      <th>pagador_inscricao_hash</th>\n", "      <th>tipo_juros</th>\n", "      <th>juros</th>\n", "      <th>tipo_multa</th>\n", "      <th>multa</th>\n", "      <th>grupo</th>\n", "      <th>pagador_estado</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-06-24</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-30</td>\n", "      <td>360.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>0</td>\n", "      <td>CPF</td>\n", "      <td>99e02f3b1ca56d979ad615782a839450b7dbabb1559110...</td>\n", "      <td>M</td>\n", "      <td>0.11</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-06-27</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-23</td>\n", "      <td>208.08</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>0</td>\n", "      <td>CPF</td>\n", "      <td>24d3131aeddadee962b8c6aa27c14e04ba5482743fb76f...</td>\n", "      <td>M</td>\n", "      <td>0.06</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-06-30</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-24</td>\n", "      <td>297.02</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>0</td>\n", "      <td>CPF</td>\n", "      <td>9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbb...</td>\n", "      <td>M</td>\n", "      <td>0.09</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-06-30</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-29</td>\n", "      <td>297.02</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>0</td>\n", "      <td>CPF</td>\n", "      <td>9d75dbc691022c487a29db4bbbdd04abc705c31767eff0...</td>\n", "      <td>M</td>\n", "      <td>0.09</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-06-30</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-07-28</td>\n", "      <td>260.55</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BANCO SANTANDER (BRASIL) S.A.</td>\n", "      <td>0</td>\n", "      <td>CPF</td>\n", "      <td>bfd5f75eb99f65b4c61617af3d2a4b4860a74f8e407ed8...</td>\n", "      <td>M</td>\n", "      <td>0.08</td>\n", "      <td>P</td>\n", "      <td>2.0</td>\n", "      <td>GF</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1635424</th>\n", "      <td>2025-06-30</td>\n", "      <td>LIQUIDADO</td>\n", "      <td>2025-08-11</td>\n", "      <td>21.78</td>\n", "      <td>2025-08-12</td>\n", "      <td>21.78</td>\n", "      <td>BANCO DO BRASIL</td>\n", "      <td>1</td>\n", "      <td>CNPJ</td>\n", "      <td>327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...</td>\n", "      <td>M</td>\n", "      <td>0.01</td>\n", "      <td>P</td>\n", "      <td>10.00</td>\n", "      <td>GW</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1635425</th>\n", "      <td>2025-06-30</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-08-25</td>\n", "      <td>21.78</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BANCO DO BRASIL</td>\n", "      <td>0</td>\n", "      <td>CNPJ</td>\n", "      <td>327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...</td>\n", "      <td>M</td>\n", "      <td>0.01</td>\n", "      <td>P</td>\n", "      <td>10.00</td>\n", "      <td>GW</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1635426</th>\n", "      <td>2025-06-30</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-09-09</td>\n", "      <td>21.78</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BANCO DO BRASIL</td>\n", "      <td>0</td>\n", "      <td>CNPJ</td>\n", "      <td>327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...</td>\n", "      <td>M</td>\n", "      <td>0.01</td>\n", "      <td>P</td>\n", "      <td>10.00</td>\n", "      <td>GW</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1635427</th>\n", "      <td>2025-06-30</td>\n", "      <td>REGISTRADO</td>\n", "      <td>2025-09-24</td>\n", "      <td>21.80</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BANCO DO BRASIL</td>\n", "      <td>0</td>\n", "      <td>CNPJ</td>\n", "      <td>327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...</td>\n", "      <td>M</td>\n", "      <td>0.01</td>\n", "      <td>P</td>\n", "      <td>10.00</td>\n", "      <td>GW</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1635428</th>\n", "      <td>2025-06-30</td>\n", "      <td>LIQUIDADO</td>\n", "      <td>2025-07-24</td>\n", "      <td>1337.21</td>\n", "      <td>2025-07-25</td>\n", "      <td>1337.21</td>\n", "      <td>BANCO DO BRASIL</td>\n", "      <td>20</td>\n", "      <td>CNPJ</td>\n", "      <td>8dc8e0ca0444a7af2af59c932a4795fcc466a048b1cf2f...</td>\n", "      <td>M</td>\n", "      <td>0.45</td>\n", "      <td>P</td>\n", "      <td>10.00</td>\n", "      <td>GW</td>\n", "      <td>SP</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1635429 rows × 16 columns</p>\n", "</div>"], "text/plain": ["        data_inclusao status_boleto data_vencto  vl_boleto    dt_pagto  \\\n", "0          2025-06-24    REGISTRADO  2025-07-30     360.00         NaN   \n", "1          2025-06-27    REGISTRADO  2025-07-23     208.08         NaN   \n", "2          2025-06-30    REGISTRADO  2025-07-24     297.02         NaN   \n", "3          2025-06-30    REGISTRADO  2025-07-29     297.02         NaN   \n", "4          2025-06-30    REGISTRADO  2025-07-28     260.55         NaN   \n", "...               ...           ...         ...        ...         ...   \n", "1635424    2025-06-30     LIQUIDADO  2025-08-11      21.78  2025-08-12   \n", "1635425    2025-06-30    REGISTRADO  2025-08-25      21.78         NaN   \n", "1635426    2025-06-30    REGISTRADO  2025-09-09      21.78         NaN   \n", "1635427    2025-06-30    REGISTRADO  2025-09-24      21.80         NaN   \n", "1635428    2025-06-30     LIQUIDADO  2025-07-24    1337.21  2025-07-25   \n", "\n", "        vl_pagto                          banco qtd_acessos_pagador  \\\n", "0            NaN  BANCO SANTANDER (BRASIL) S.A.                   0   \n", "1            NaN  BANCO SANTANDER (BRASIL) S.A.                   0   \n", "2            NaN  BANCO SANTANDER (BRASIL) S.A.                   0   \n", "3            NaN  BANCO SANTANDER (BRASIL) S.A.                   0   \n", "4            NaN  BANCO SANTANDER (BRASIL) S.A.                   0   \n", "...          ...                            ...                 ...   \n", "1635424    21.78                BANCO DO BRASIL                   1   \n", "1635425      NaN                BANCO DO BRASIL                   0   \n", "1635426      NaN                BANCO DO BRASIL                   0   \n", "1635427      NaN                BANCO DO BRASIL                   0   \n", "1635428  1337.21                BANCO DO BRASIL                  20   \n", "\n", "        pagador_cnpjcpf                             pagador_inscricao_hash  \\\n", "0                   CPF  99e02f3b1ca56d979ad615782a839450b7dbabb1559110...   \n", "1                   CPF  24d3131aeddadee962b8c6aa27c14e04ba5482743fb76f...   \n", "2                   CPF  9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbb...   \n", "3                   CPF  9d75dbc691022c487a29db4bbbdd04abc705c31767eff0...   \n", "4                   CPF  bfd5f75eb99f65b4c61617af3d2a4b4860a74f8e407ed8...   \n", "...                 ...                                                ...   \n", "1635424            CNPJ  327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...   \n", "1635425            CNPJ  327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...   \n", "1635426            CNPJ  327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...   \n", "1635427            CNPJ  327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...   \n", "1635428            CNPJ  8dc8e0ca0444a7af2af59c932a4795fcc466a048b1cf2f...   \n", "\n", "        tipo_juros  juros tipo_multa  multa grupo pagador_estado  \n", "0                M   0.11          P    2.0    GF             SP  \n", "1                M   0.06          P    2.0    GF             SP  \n", "2                M   0.09          P    2.0    GF             SP  \n", "3                M   0.09          P    2.0    GF             SP  \n", "4                M   0.08          P    2.0    GF             SP  \n", "...            ...    ...        ...    ...   ...            ...  \n", "1635424          M   0.01          P  10.00    GW             SP  \n", "1635425          M   0.01          P  10.00    GW             SP  \n", "1635426          M   0.01          P  10.00    GW             SP  \n", "1635427          M   0.01          P  10.00    GW             SP  \n", "1635428          M   0.45          P  10.00    GW             SP  \n", "\n", "[1635429 rows x 16 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Valores faltantes por coluna:\n", "data_inclusao                  0\n", "status_boleto                  0\n", "data_vencto                    0\n", "vl_boleto                      0\n", "dt_pagto                  631784\n", "vl_pagto                  628936\n", "banco                          0\n", "qtd_acessos_pagador            0\n", "pagador_cnpjcpf                0\n", "pagador_inscricao_hash         0\n", "tipo_juros                     0\n", "juros                          0\n", "tipo_multa                     0\n", "multa                          0\n", "grupo                          0\n", "pagador_estado                 0\n", "dtype: int64\n"]}], "source": ["# Atualizando o DataFrame principal\n", "dados[\"qtd_acessos_pagador\"] = dados[\"qtd_acessos_pagador\"].fillna(0)\n", "dados[\"tipo_multa\"] = dados[\"tipo_multa\"].fillna(\"Não há multa\")\n", "dados[\"multa\"] = dados[\"multa\"].fillna(0)\n", "display(dados)\n", "print(\"Valores faltantes por coluna:\")\n", "print(dados.isnull().sum())"]}, {"cell_type": "markdown", "id": "360da95e", "metadata": {}, "source": ["label encoding"]}, {"cell_type": "code", "execution_count": 11, "id": "b83f2283", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mapeamento: <PERSON><PERSON><PERSON> → Valor Original\n", "\n", "🔹 Coluna: 'grupo'\n", "   1 → GF\n", "   2 → GL\n", "   3 → GM\n", "   4 → GP\n", "   5 → GT\n", "   6 → GV\n", "   7 → GW\n", "--------------------------------------------------\n", "🔹 Coluna: 'status_boleto'\n", "   1 → BAIXADO\n", "   2 → LIQUIDADO\n", "   3 → PROTESTADO\n", "   4 → REGISTRADO\n", "--------------------------------------------------\n", "🔹 Coluna: 'banco'\n", "   1 → BANCO ABC BRASIL S.A.\n", "   2 → BANCO BBM BOCOM\n", "   3 → BANCO BRADESCO SA\n", "   4 → BANCO CITIBANK S.A.\n", "   5 → BANCO DAYCOVAL\n", "   6 → BANCO DO BRASIL\n", "   7 → BANCO SAFRA\n", "   8 → BANCO SANTANDER (BRASIL) S.A.\n", "   9 → BANCO VOTORANTIM S.A.\n", "   10 → CAIXA ECONOMICA FEDERAL\n", "   11 → China Construction Bank (brasil) Banco Multiplo S/a\n", "   12 → ITAU UNIBANCO S.A.\n", "--------------------------------------------------\n", "🔹 Coluna: 'pagador_estado'\n", "   1 → AL\n", "   2 → AM\n", "   3 → AP\n", "   4 → BA\n", "   5 → CE\n", "   6 → DF\n", "   7 → ES\n", "   8 → Estado não informado\n", "   9 → GO\n", "   10 → MA\n", "   11 → MG\n", "   12 → MS\n", "   13 → MT\n", "   14 → PA\n", "   15 → PB\n", "   16 → PE\n", "   17 → PI\n", "   18 → <PERSON>\n", "   19 → RJ\n", "   20 → RN\n", "   21 → RS\n", "   22 → SC\n", "   23 → SE\n", "   24 → SP\n", "   25 → TO\n", "--------------------------------------------------\n", "🔹 Coluna: 'pagador_cnpjcpf'\n", "   1 → CNPJ\n", "   2 → CPF\n", "--------------------------------------------------\n", "🔹 Coluna: 'tipo_juros'\n", "   1 → M\n", "   2 → P\n", "   3 → S\n", "--------------------------------------------------\n", "🔹 Coluna: 'tipo_multa'\n", "   1 → M\n", "   2 → Não há multa\n", "   3 → P\n", "--------------------------------------------------\n"]}], "source": ["# Dicionário para armazenar todos os mapeamentos\n", "mapa_mapeamentos = {}\n", "\n", "# Colunas que você quer codificar\n", "colunas_para_codificar = [\n", "    \"grupo\",\n", "    \"status_boleto\",\n", "    \"banco\",\n", "    \"pagador_estado\",\n", "    \"pagador_cnpjcpf\",\n", "    \"tipo_juros\",\n", "    \"tipo_multa\",\n", "]\n", "\n", "print(\"Mapeamento: Código → Valor Original\\n\")\n", "for col in colunas_para_codificar:\n", "    # Converter para categórico para garantir tratamento correto\n", "    cat = pd.Categorical(dados[col])\n", "    \n", "    # Criar mapeamento: código (começando em 1) → valor original\n", "    mapeamento = {i + 1: categoria for i, categoria in enumerate(cat.categories)}\n", "    \n", "    # Salvar no dicionário geral\n", "    mapa_mapeamentos[col] = mapeamento\n", "    \n", "    # Aplicar o encoding no DataFrame\n", "    dados[col] = cat.codes + 1  # +1 para começar de 1, não de 0\n", "    \n", "    # <PERSON><PERSON><PERSON> map<PERSON>\n", "    print(f\"🔹 Coluna: '{col}'\")\n", "    for codigo, valor in sorted(mapeamento.items()):\n", "        print(f\"   {codigo} → {valor}\")\n", "    print(\"-\" * 50)"]}, {"cell_type": "markdown", "id": "1ee95555", "metadata": {}, "source": ["verificando o tipo de coluna"]}, {"cell_type": "code", "execution_count": 12, "id": "f940bf64", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["data_inclusao              object\n", "status_boleto                int8\n", "data_vencto                object\n", "vl_boleto                 float64\n", "dt_pagto                   object\n", "vl_pagto                   object\n", "banco                        int8\n", "qtd_acessos_pagador        object\n", "pagador_cnpjcpf              int8\n", "pagador_inscricao_hash     object\n", "tipo_juros                   int8\n", "juros                     float64\n", "tipo_multa                   int8\n", "multa                      object\n", "grupo                        int8\n", "pagador_estado               int8\n", "dtype: object\n"]}], "source": ["print(dados.dtypes)"]}, {"cell_type": "code", "execution_count": 13, "id": "d204eb3a", "metadata": {}, "outputs": [], "source": ["# 1. Converter colunas de data\n", "colunas_data = ['data_inclusao', 'data_vencto', 'dt_pagto']\n", "\n", "for col in colunas_data:\n", "    dados[col] = pd.to_datetime(dados[col], errors='coerce')\n", "    # errors='coerce' → transforma valores inválidos em NaT (Not a Time)\n", "\n", "# 2. Converter colunas numéricas que estão como object\n", "#    (vl_pagto, multa, qtd_acessos_pagador)\n", "\n", "# Primeiro: garan<PERSON><PERSON> que são numéricas, convertendo strings\n", "dados[\"vl_pagto\"] = pd.to_numeric(dados[\"vl_pagto\"], errors='coerce')\n", "dados[\"multa\"] = pd.to_numeric(dados[\"multa\"], errors='coerce')\n", "dados[\"qtd_acessos_pagador\"] = pd.to_numeric(dados[\"qtd_acessos_pagador\"], errors='coerce')\n", "dados[\"pagador_estado\"] = pd.to_numeric(dados[\"pagador_estado\"], errors='coerce')\n", "\n", "\n", "# Converter para int ou float (opcional: int64 ou float64)\n", "dados[\"vl_pagto\"] = dados[\"vl_pagto\"].astype('float64')\n", "dados[\"multa\"] = dados[\"multa\"].astype('float64')\n", "dados[\"qtd_acessos_pagador\"] = dados[\"qtd_acessos_pagador\"].astype('int64')\n", "dados[\"pagador_estado\"] = dados[\"pagador_estado\"].astype('int64')"]}, {"cell_type": "code", "execution_count": 14, "id": "13010167", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["data_inclusao             datetime64[ns]\n", "status_boleto                       int8\n", "data_vencto               datetime64[ns]\n", "vl_boleto                        float64\n", "dt_pagto                  datetime64[ns]\n", "vl_pagto                         float64\n", "banco                               int8\n", "qtd_acessos_pagador                int64\n", "pagador_cnpjcpf                     int8\n", "pagador_inscricao_hash            object\n", "tipo_juros                          int8\n", "juros                            float64\n", "tipo_multa                          int8\n", "multa                            float64\n", "grupo                               int8\n", "pagador_estado                     int64\n", "dtype: object\n"]}], "source": ["print(dados.dtypes)"]}, {"cell_type": "markdown", "id": "0cf4cd05", "metadata": {}, "source": ["coluna de dias para vencimento"]}, {"cell_type": "code", "execution_count": 15, "id": "47af8069", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "⚠️  1447 boletos têm vencimento ANTES da inclusão (erro?)\n", "      data_inclusao data_vencto  dias_para_vencimento\n", "14292    2024-07-03  2024-07-02                  -1.0\n", "14344    2024-07-03  2024-07-02                  -1.0\n", "30241    2025-03-06  2024-07-17                -232.0\n", "30242    2025-03-06  2024-08-16                -202.0\n", "30243    2025-03-06  2024-09-16                -171.0\n"]}], "source": ["# Calcular a diferença em dias\n", "dados['dias_para_vencimento'] = (dados['data_vencto'] - dados['data_inclusao']).dt.days\n", "\n", "# Verificar valores negativos (vencimento antes da inclusão)\n", "negativos = dados[dados['dias_para_vencimento'] < 0]\n", "if len(negativos) > 0:\n", "    print(f\"\\n⚠️  {len(negativos)} boletos têm vencimento ANTES da inclusão (erro?)\")\n", "    print(negativos[['data_inclusao', 'data_vencto', 'dias_para_vencimento']].head())"]}, {"cell_type": "markdown", "id": "47db23bc", "metadata": {}, "source": ["separar tabelas de boletos pagos e não pagos"]}, {"cell_type": "code", "execution_count": 16, "id": "d5077b22", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total de registros: 1635429\n", "<PERSON> aberto (sem data de pagamento): 631784\n", "Pagos (com data de pagamento): 1003645\n"]}], "source": ["# 1. Separar linhas onde dt_pagto <PERSON> NaN (boletos não pagos) → \"em aberto\"\n", "dados_em_aberto = dados[dados[\"dt_pagto\"].isnull()].copy()\n", "dados_pagos = dados[dados[\"dt_pagto\"].notnull()].copy()\n", "\n", "print(f\"Total de registros: {len(dados)}\")\n", "print(f\"Em aberto (sem data de pagamento): {len(dados_em_aberto)}\")\n", "print(f\"Pagos (com data de pagamento): {len(dados_pagos)}\")"]}, {"cell_type": "code", "execution_count": 17, "id": "c66bfce6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Val<PERSON> faltantes dados_em_aberto:\n", "data_inclusao                  0\n", "status_boleto                  0\n", "data_vencto                    0\n", "vl_boleto                      0\n", "dt_pagto                  631784\n", "vl_pagto                  628936\n", "banco                          0\n", "qtd_acessos_pagador            0\n", "pagador_cnpjcpf                0\n", "pagador_inscricao_hash         0\n", "tipo_juros                     0\n", "juros                          0\n", "tipo_multa                     0\n", "multa                          0\n", "grupo                          0\n", "pagador_estado                 0\n", "dias_para_vencimento           0\n", "dtype: int64\n", "Valores faltantes dados_pagos:\n", "data_inclusao             0\n", "status_boleto             0\n", "data_vencto               1\n", "vl_boleto                 0\n", "dt_pagto                  0\n", "vl_pagto                  0\n", "banco                     0\n", "qtd_acessos_pagador       0\n", "pagador_cnpjcpf           0\n", "pagador_inscricao_hash    0\n", "tipo_juros                0\n", "juros                     0\n", "tipo_multa                0\n", "multa                     0\n", "grupo                     0\n", "pagador_estado            0\n", "dias_para_vencimento      1\n", "dtype: int64\n"]}], "source": ["print(\"Valores faltantes dados_em_aberto:\")\n", "print(dados_em_aberto.isnull().sum())\n", "print(\"Valores faltantes dados_pagos:\")\n", "print(dados_pagos.isnull().sum())"]}, {"cell_type": "code", "execution_count": 18, "id": "389b397a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Linha com data_vencto faltante:\n", "       data_inclusao  vl_boleto  banco  grupo  pagador_estado data_vencto\n", "145054    2024-10-09    4089.54     12      3              11         NaT\n"]}], "source": ["# Filtrar a linha com data_vencto faltante\n", "linha_faltante = dados_pagos[dados_pagos['data_vencto'].isna()]\n", "dados_pagos = dados_pagos.dropna(subset=['data_vencto']).reset_index(drop=True)\n", "\n", "print(\"Linha com data_vencto faltante:\")\n", "print(linha_faltante[['data_inclusao', 'vl_boleto', 'banco', 'grupo', 'pagador_estado', 'data_vencto']])"]}, {"cell_type": "code", "execution_count": 19, "id": "65e1b31d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Val<PERSON> faltantes dados_em_aberto:\n", "data_inclusao                  0\n", "status_boleto                  0\n", "data_vencto                    0\n", "vl_boleto                      0\n", "dt_pagto                  631784\n", "vl_pagto                  628936\n", "banco                          0\n", "qtd_acessos_pagador            0\n", "pagador_cnpjcpf                0\n", "pagador_inscricao_hash         0\n", "tipo_juros                     0\n", "juros                          0\n", "tipo_multa                     0\n", "multa                          0\n", "grupo                          0\n", "pagador_estado                 0\n", "dias_para_vencimento           0\n", "dtype: int64\n", "Valores faltantes dados_pagos:\n", "data_inclusao             0\n", "status_boleto             0\n", "data_vencto               0\n", "vl_boleto                 0\n", "dt_pagto                  0\n", "vl_pagto                  0\n", "banco                     0\n", "qtd_acessos_pagador       0\n", "pagador_cnpjcpf           0\n", "pagador_inscricao_hash    0\n", "tipo_juros                0\n", "juros                     0\n", "tipo_multa                0\n", "multa                     0\n", "grupo                     0\n", "pagador_estado            0\n", "dias_para_vencimento      0\n", "dtype: int64\n"]}], "source": ["print(\"Valores faltantes dados_em_aberto:\")\n", "print(dados_em_aberto.isnull().sum())\n", "print(\"Valores faltantes dados_pagos:\")\n", "print(dados_pagos.isnull().sum())"]}, {"cell_type": "markdown", "id": "49da48f7", "metadata": {}, "source": ["colunas novas"]}, {"cell_type": "code", "execution_count": 20, "id": "0dc6b793", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["situacao_pagamento\n", "Pago com até 30 dias de atraso     838960\n", "Pago antes do vencimento           159214\n", "Inadimplente (atraso > 30 dias)      5470\n", "Name: count, dtype: int64\n", "Mapeamento da situação de pagamento:\n", "   1 → Pago antes do vencimento\n", "   2 → Pago com até 30 dias de atraso\n", "   3 → Inadimplente (atraso > 30 dias)\n"]}], "source": ["# Calcular a diferença em dias (pagamento - vencimento)\n", "diferenca_dias = (dados_pagos['dt_pagto'] - dados_pagos['data_vencto']).dt.days\n", "\n", "# Criar a nova coluna com as 3 categorias\n", "condicoes = [\n", "    # 1. <PERSON><PERSON> antes do vencimento\n", "    (diferenca_dias < 0),\n", "    # 2. <PERSON><PERSON> até 30 dias após o vencimento (em dia/almoço)\n", "    (diferenca_dias >= 0) & (diferenca_dias <= 30),\n", "    # 3. Pago com mais de 30 dias de atraso (inadimplente)\n", "    (diferen<PERSON>_dias > 30)\n", "]\n", "\n", "valores = [\n", "    'Pago antes do vencimento',\n", "    'Pago com até 30 dias de atraso',\n", "    'Inadimplente (atraso > 30 dias)'\n", "]\n", "\n", "dados_pagos['situacao_pagamento'] = np.select(condicoes, valores, default='Erro')\n", "\n", "# Verificar contagem\n", "print(dados_pagos['situacao_pagamento'].value_counts(dropna=False))\n", "\n", "# Mapeamento explícito (recomendado para controle)\n", "mapeamento_situacao = {\n", "    'Pago antes do vencimento': 1,\n", "    'Pago com até 30 dias de atraso': 2,\n", "    'Inadimplente (atraso > 30 dias)': 3\n", "}\n", "\n", "# Aplicar o encoding\n", "dados_pagos['situacao_pagamento'] = dados_pagos['situacao_pagamento'].map(mapeamento_situacao)\n", "\n", "print(\"Mapeamento da situação de pagamento:\")\n", "for descricao, codigo in mapeamento_situacao.items():\n", "    print(f\"   {codigo} → {descricao}\")\n"]}, {"cell_type": "markdown", "id": "002b42a4", "metadata": {}, "source": ["tabelas finais"]}, {"cell_type": "code", "execution_count": 21, "id": "73f18493", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "data_inclusao", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "status_boleto", "rawType": "int8", "type": "integer"}, {"name": "data_vencto", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "vl_boleto", "rawType": "float64", "type": "float"}, {"name": "dt_pagto", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "vl_pagto", "rawType": "float64", "type": "float"}, {"name": "banco", "rawType": "int8", "type": "integer"}, {"name": "qtd_acessos_pagador", "rawType": "int64", "type": "integer"}, {"name": "pagador_cnpjcpf", "rawType": "int8", "type": "integer"}, {"name": "pagador_inscricao_hash", "rawType": "object", "type": "string"}, {"name": "tipo_juros", "rawType": "int8", "type": "integer"}, {"name": "juros", "rawType": "float64", "type": "float"}, {"name": "tipo_multa", "rawType": "int8", "type": "integer"}, {"name": "multa", "rawType": "float64", "type": "float"}, {"name": "grupo", "rawType": "int8", "type": "integer"}, {"name": "pagador_estado", "rawType": "int64", "type": "integer"}, {"name": "dias_para_vencimento", "rawType": "float64", "type": "float"}, {"name": "situacao_pagamento", "rawType": "int64", "type": "integer"}], "ref": "f4dbb96f-2904-4f53-8eab-240d5fe9cfc6", "rows": [["0", "2024-07-01 00:00:00", "2", "2024-07-22 00:00:00", "283.42", "2024-07-03 00:00:00", "263.58", "8", "2", "2", "f5e3d835f189bcdc3c06e012a54a648938e587ecf752a8cf12d26bf8f54b6c3a", "1", "0.08", "3", "2.0", "1", "24", "21.0", "1"], ["1", "2024-07-01 00:00:00", "2", "2024-07-22 00:00:00", "330.42", "2024-07-03 00:00:00", "307.29", "8", "2", "2", "8cafe0ee5d35f8d717a9165a3a9cfd284dd080adc977cd42e18efd9b88032138", "1", "0.1", "3", "2.0", "1", "24", "21.0", "1"], ["2", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "374.15", "2024-07-30 00:00:00", "382.39", "8", "4", "2", "fa65b15f453acb990f8492a5b30a58849d0c87985a0a90c500593ae50b02c677", "1", "0.11", "3", "2.0", "1", "24", "19.0", "2"], ["3", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "324.13", "2024-07-16 00:00:00", "324.13", "8", "0", "2", "db63183e80b196093f7a06ee4affc35b89daa05849412f906dae012dc3ea481a", "1", "0.1", "3", "2.0", "1", "24", "19.0", "1"], ["4", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "337.0", "2024-07-23 00:00:00", "337.0", "8", "0", "2", "a0b12a8471aec5c6605a529cf284990656af7385bc5e8567fe48b9350d2ecc1b", "1", "0.1", "3", "2.0", "1", "24", "19.0", "2"], ["5", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "283.42", "2024-07-23 00:00:00", "283.42", "8", "0", "2", "74fae55a6c42fc13d907daadf3ee1b9ea0366dc057cfe3eb57a1c77c11b2aa9c", "1", "0.08", "3", "2.0", "1", "24", "19.0", "2"], ["6", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "305.18", "2024-07-11 00:00:00", "305.18", "8", "0", "2", "eaf3129b8aa5efecfd193082fea0ce0d4e6ecfe86c89ca6507bcc261749ce826", "1", "0.09", "3", "2.0", "1", "24", "19.0", "1"], ["7", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "325.52", "2024-07-10 00:00:00", "302.73", "8", "0", "2", "296737eff387c7d8cd675f2a8b8a2ec51cae0152c859d4023be1fb46b2016187", "1", "0.1", "3", "2.0", "1", "24", "19.0", "1"], ["8", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "325.52", "2024-07-16 00:00:00", "325.52", "8", "0", "2", "c2868f8f45ee6970387115d89e745cc8eb5889ababa833b331e93ed54e3f5e08", "1", "0.1", "3", "2.0", "1", "24", "19.0", "1"], ["9", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "305.17", "2024-07-08 00:00:00", "283.81", "8", "0", "2", "2ec824e754a6ecef074a824650ce8f4e7f367789f28670d483e10739870a69d6", "1", "0.09", "3", "2.0", "1", "24", "19.0", "1"], ["10", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "327.92", "2024-07-08 00:00:00", "304.97", "8", "0", "2", "08a45924ac854b3d2c311f7f12995d4c273131cd2685eb95298d57f1476eb528", "1", "0.1", "3", "2.0", "1", "24", "19.0", "1"], ["11", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "212.56", "2024-07-08 00:00:00", "197.68", "8", "0", "2", "6649b728134dbcd4295d0abc5e64c4e61322163bc09d919e37faf294904ab21b", "1", "0.06", "3", "2.0", "1", "24", "19.0", "1"], ["12", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "325.52", "2024-07-16 00:00:00", "325.52", "8", "0", "2", "9f2c2df6c1d9a81666ebf02b2f0fa7a19bf28e793d5ad6106a09603484b7b937", "1", "0.1", "3", "2.0", "1", "24", "19.0", "1"], ["13", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "337.0", "2024-07-18 00:00:00", "337.0", "8", "0", "2", "f8cff0e00872e9b3f567ca063988a6c19b76d050ec3045a853a96d8b854d6431", "1", "0.1", "3", "2.0", "1", "24", "19.0", "1"], ["14", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "244.14", "2024-07-11 00:00:00", "227.05", "8", "0", "2", "309f0de52b7d17ddfbf5dfea92a77dcae81f463316e9f6b02031097d1bd1ec77", "1", "0.07", "3", "2.0", "1", "24", "19.0", "1"], ["15", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "325.52", "2024-07-09 00:00:00", "302.73", "8", "2", "2", "1e9fc6dbc01f4c1105fa18f724863d4f51eacdaf311e38ef5d32eb95a4469407", "1", "0.1", "3", "2.0", "1", "24", "19.0", "1"], ["16", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "325.52", "2024-07-11 00:00:00", "302.73", "8", "0", "2", "423cf279e4dc04e10986e82cc4973b2244542092c55ab684370046e0bd148887", "1", "0.1", "3", "2.0", "1", "24", "19.0", "1"], ["17", "2024-07-03 00:00:00", "2", "2024-07-20 00:00:00", "283.42", "2024-07-08 00:00:00", "263.58", "8", "0", "2", "625f6dd1b70233b75bdc9143d338a86907c26fa2d31321b13fcfd5e85f3641e6", "1", "0.08", "3", "2.0", "1", "24", "17.0", "1"], ["18", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "212.56", "2024-07-23 00:00:00", "212.56", "8", "0", "2", "9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbbc3b7ba5bef574a2a04", "1", "0.06", "3", "2.0", "1", "24", "19.0", "2"], ["19", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "264.66", "2024-07-11 00:00:00", "246.13", "8", "0", "2", "96beb1427803b97f37a165e6f2c98cc8d60b4c924c3b003f1fc9cc351cd12067", "1", "0.08", "3", "2.0", "1", "24", "19.0", "1"], ["20", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "155.7", "2024-07-08 00:00:00", "144.8", "8", "0", "2", "1279aa233b3da7b6686e2ab21082a304e49742777b2f7415984d03711791b7ce", "1", "0.05", "3", "2.0", "1", "24", "19.0", "1"], ["21", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "155.37", "2024-07-05 00:00:00", "144.49", "8", "0", "2", "b0d1325c1173bd3032d40f3d8fd727a46900620314515a6355570a7bbe1b9d10", "1", "0.05", "3", "2.0", "1", "24", "19.0", "1"], ["22", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "155.37", "2024-07-16 00:00:00", "155.37", "8", "0", "2", "3c4d79de3360028aa3a8c5467e7f760bb09a31ced9c47e7648f0810597b0ec11", "1", "0.05", "3", "2.0", "1", "24", "19.0", "1"], ["23", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "155.37", "2024-07-09 00:00:00", "144.49", "8", "0", "2", "5346efdff4f5e86e785953bcc9d9173edd4f1be13a556598a5c99301550bc96a", "1", "0.05", "3", "2.0", "1", "24", "19.0", "1"], ["24", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "155.37", "2024-07-10 00:00:00", "144.49", "8", "0", "2", "bce1aef98d2ed4e4003dcfaf7784a35feb612bdb79f79f4d3d89d824c6f57ccc", "1", "0.05", "3", "2.0", "1", "24", "19.0", "1"], ["25", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "155.37", "2024-07-17 00:00:00", "155.37", "8", "0", "2", "0832922c8e3584b858dcf8ff12894b8e8054c78cff5848ab5d4d106a7790a46f", "1", "0.05", "3", "2.0", "1", "24", "19.0", "1"], ["26", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "293.45", "2024-07-05 00:00:00", "272.91", "8", "0", "2", "f673e845df1044043f739aacf5cb6f1531a6c0d827ecab3c417bf5205a358214", "1", "0.09", "3", "2.0", "1", "24", "19.0", "1"], ["27", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "265.7", "2024-07-11 00:00:00", "247.1", "8", "0", "2", "de44b5871865677afc2b3b41dfbcb44841e93d5b512f8bd512ead46175406777", "1", "0.08", "3", "2.0", "1", "24", "19.0", "1"], ["28", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "305.18", "2024-07-09 00:00:00", "283.82", "8", "0", "2", "15582f51a161603f38dc5c24f129ca2806808fd8618af671b97a0899301f2c6c", "1", "0.09", "3", "2.0", "1", "24", "19.0", "1"], ["29", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "302.46", "2024-07-11 00:00:00", "281.29", "8", "0", "2", "163836a12f1e2848c553c21ab3fc4db703b9bb6ed0b71283475829076b8a65ee", "1", "0.09", "3", "2.0", "1", "24", "19.0", "1"], ["30", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "325.52", "2024-07-08 00:00:00", "302.73", "8", "0", "2", "fd642efbc95cf5c98d7c9d8964b9fc47af166980e9c29dccc21ea1bd95670344", "1", "0.1", "3", "2.0", "1", "24", "19.0", "1"], ["31", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "325.52", "2024-07-30 00:00:00", "332.72", "8", "0", "2", "b0f1bbc75f9ba96877bf541fc717e7884c36066833cdde8fe5c972b92c4799f7", "1", "0.1", "3", "2.0", "1", "24", "19.0", "2"], ["32", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "325.52", "2024-07-09 00:00:00", "302.73", "8", "0", "2", "d0e197adbd494488def802f184c9148b5dd99346882e18ac2d415c9233c13e3c", "1", "0.1", "3", "2.0", "1", "24", "19.0", "1"], ["33", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "355.61", "2024-07-11 00:00:00", "330.72", "8", "0", "2", "4c386425e17ccfba060818e28f9f006583eb993f65c48b1197ee6be02375b41d", "1", "0.11", "3", "2.0", "1", "24", "19.0", "1"], ["34", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "283.42", "2024-07-16 00:00:00", "283.42", "8", "0", "2", "14eb4d9ccd43f7fb8e2d2d67228746536871d52ef442a1fb31f068f9037d9f0c", "1", "0.08", "3", "2.0", "1", "24", "19.0", "1"], ["35", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "325.52", "2024-07-08 00:00:00", "302.73", "8", "0", "2", "aa3324888fc1d2abe01a6a9c32cdb8c7498072a6c497bab98bad0e034bc757f4", "1", "0.1", "3", "2.0", "1", "24", "19.0", "1"], ["36", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "355.61", "2024-07-11 00:00:00", "330.72", "8", "2", "2", "ecdb43ad8633ba8622e99b79caa49f915c59c5ddbbc97d7fa03ffd98fea1f18d", "1", "0.11", "3", "2.0", "1", "24", "19.0", "1"], ["37", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "374.35", "2024-07-23 00:00:00", "374.35", "8", "0", "2", "1613d8b0de42ccf90eff61ba5d5c01bfb5c137c802c656b2a278bae4b489c7fd", "1", "0.11", "3", "2.0", "1", "24", "19.0", "2"], ["38", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "293.45", "2024-07-11 00:00:00", "272.91", "8", "0", "2", "eaf3129b8aa5efecfd193082fea0ce0d4e6ecfe86c89ca6507bcc261749ce826", "1", "0.09", "3", "2.0", "1", "24", "19.0", "1"], ["39", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "305.17", "2024-07-15 00:00:00", "305.17", "8", "0", "2", "1975ecc0738cf16ef789185955698196cbb64fe07af93560f0af8c2b276937ef", "1", "0.09", "3", "2.0", "1", "24", "19.0", "1"], ["40", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "283.42", "2024-07-05 00:00:00", "263.58", "8", "0", "2", "45a31ce7f14f018e558b382f7242b0fe8093c5e5ba912497d208cc36100bb404", "1", "0.08", "3", "2.0", "1", "24", "19.0", "1"], ["41", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "305.17", "2024-07-15 00:00:00", "305.17", "8", "0", "2", "f65b88986a0dc2579bbca4fabff0a5e77443af7c5e942f94c1e413b0e316b301", "1", "0.09", "3", "2.0", "1", "24", "19.0", "1"], ["42", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "283.42", "2024-07-31 00:00:00", "289.73", "8", "2", "2", "b210bf3ddf232ac04eae8b37b7893b46e447f3b5f0eed6c9ca08b1522fb415c0", "1", "0.08", "3", "2.0", "1", "24", "19.0", "2"], ["43", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "283.42", "2024-07-05 00:00:00", "283.42", "8", "0", "2", "4ea16bbc129938446afdab43b6ec39a4efaf0daae7edd9ede738f2ecfd0e582e", "1", "0.08", "3", "2.0", "1", "24", "19.0", "1"], ["44", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "283.42", "2024-07-10 00:00:00", "263.58", "8", "0", "2", "e40f0ea47b904ce5a797b1bb96a86b09f76babb95281bd65b438872e168ed215", "1", "0.08", "3", "2.0", "1", "24", "19.0", "1"], ["45", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "283.42", "2024-07-23 00:00:00", "283.42", "8", "6", "2", "e7e33eb910be914d5f90c14bb38c4637259b6e601f3ca223caa4e0c329a526af", "1", "0.08", "3", "2.0", "1", "24", "19.0", "2"], ["46", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "283.42", "2024-07-05 00:00:00", "263.58", "8", "2", "2", "0cf85583fd144104467e5f51e1b61e81ab2973f64ff984093bee3eeb4e5ddc44", "1", "0.08", "3", "2.0", "1", "24", "19.0", "1"], ["47", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "265.7", "2024-07-11 00:00:00", "247.1", "8", "0", "2", "a3ac77b3bc2d61a8b5c3c240c75f126cf1b8f8d3f9ae8e80c00a2ef0beb20347", "1", "0.08", "3", "2.0", "1", "24", "19.0", "1"], ["48", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "283.42", "2024-07-11 00:00:00", "263.58", "8", "0", "2", "12c404be7205d6cf17554b3fc1f9b11bc904e752735672ced44c44e872783456", "1", "0.08", "3", "2.0", "1", "24", "19.0", "1"], ["49", "2024-07-03 00:00:00", "2", "2024-07-22 00:00:00", "325.52", "2024-07-11 00:00:00", "302.73", "8", "0", "2", "2dc3f8a7b12a81926fbd50e7f56f2f461c22faa1480c2ecc9c18a7dde8c88d77", "1", "0.1", "3", "2.0", "1", "24", "19.0", "1"]], "shape": {"columns": 18, "rows": 1003644}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>data_inclusao</th>\n", "      <th>status_boleto</th>\n", "      <th>data_vencto</th>\n", "      <th>vl_boleto</th>\n", "      <th>dt_pagto</th>\n", "      <th>vl_pagto</th>\n", "      <th>banco</th>\n", "      <th>qtd_acessos_pagador</th>\n", "      <th>pagador_cnpjcpf</th>\n", "      <th>pagador_inscricao_hash</th>\n", "      <th>tipo_juros</th>\n", "      <th>juros</th>\n", "      <th>tipo_multa</th>\n", "      <th>multa</th>\n", "      <th>grupo</th>\n", "      <th>pagador_estado</th>\n", "      <th>dias_para_vencimento</th>\n", "      <th>situacao_pagamento</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-07-01</td>\n", "      <td>2</td>\n", "      <td>2024-07-22</td>\n", "      <td>283.42</td>\n", "      <td>2024-07-03</td>\n", "      <td>263.58</td>\n", "      <td>8</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>f5e3d835f189bcdc3c06e012a54a648938e587ecf752a8...</td>\n", "      <td>1</td>\n", "      <td>0.08</td>\n", "      <td>3</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>21.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-07-01</td>\n", "      <td>2</td>\n", "      <td>2024-07-22</td>\n", "      <td>330.42</td>\n", "      <td>2024-07-03</td>\n", "      <td>307.29</td>\n", "      <td>8</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>8cafe0ee5d35f8d717a9165a3a9cfd284dd080adc977cd...</td>\n", "      <td>1</td>\n", "      <td>0.10</td>\n", "      <td>3</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>21.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-07-03</td>\n", "      <td>2</td>\n", "      <td>2024-07-22</td>\n", "      <td>374.15</td>\n", "      <td>2024-07-30</td>\n", "      <td>382.39</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>fa65b15f453acb990f8492a5b30a58849d0c87985a0a90...</td>\n", "      <td>1</td>\n", "      <td>0.11</td>\n", "      <td>3</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>19.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-07-03</td>\n", "      <td>2</td>\n", "      <td>2024-07-22</td>\n", "      <td>324.13</td>\n", "      <td>2024-07-16</td>\n", "      <td>324.13</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>db63183e80b196093f7a06ee4affc35b89daa05849412f...</td>\n", "      <td>1</td>\n", "      <td>0.10</td>\n", "      <td>3</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>19.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-07-03</td>\n", "      <td>2</td>\n", "      <td>2024-07-22</td>\n", "      <td>337.00</td>\n", "      <td>2024-07-23</td>\n", "      <td>337.00</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>a0b12a8471aec5c6605a529cf284990656af7385bc5e85...</td>\n", "      <td>1</td>\n", "      <td>0.10</td>\n", "      <td>3</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>19.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1003639</th>\n", "      <td>2025-06-30</td>\n", "      <td>2</td>\n", "      <td>2025-07-28</td>\n", "      <td>34161.47</td>\n", "      <td>2025-07-29</td>\n", "      <td>34161.47</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>7a149f96b2c656019a5eeda15e46a44b911d4aabb59cba...</td>\n", "      <td>1</td>\n", "      <td>11.39</td>\n", "      <td>3</td>\n", "      <td>10.0</td>\n", "      <td>7</td>\n", "      <td>22</td>\n", "      <td>28.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1003640</th>\n", "      <td>2025-06-30</td>\n", "      <td>2</td>\n", "      <td>2025-07-24</td>\n", "      <td>1170.92</td>\n", "      <td>2025-07-25</td>\n", "      <td>1170.92</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>26b1451db9083ef0e21a93d83d50cd508e2daa484b006e...</td>\n", "      <td>1</td>\n", "      <td>0.39</td>\n", "      <td>3</td>\n", "      <td>10.0</td>\n", "      <td>7</td>\n", "      <td>24</td>\n", "      <td>24.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1003641</th>\n", "      <td>2025-06-30</td>\n", "      <td>2</td>\n", "      <td>2025-07-28</td>\n", "      <td>21.78</td>\n", "      <td>2025-07-29</td>\n", "      <td>21.78</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...</td>\n", "      <td>1</td>\n", "      <td>0.01</td>\n", "      <td>3</td>\n", "      <td>10.0</td>\n", "      <td>7</td>\n", "      <td>24</td>\n", "      <td>28.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1003642</th>\n", "      <td>2025-06-30</td>\n", "      <td>2</td>\n", "      <td>2025-08-11</td>\n", "      <td>21.78</td>\n", "      <td>2025-08-12</td>\n", "      <td>21.78</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...</td>\n", "      <td>1</td>\n", "      <td>0.01</td>\n", "      <td>3</td>\n", "      <td>10.0</td>\n", "      <td>7</td>\n", "      <td>24</td>\n", "      <td>42.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1003643</th>\n", "      <td>2025-06-30</td>\n", "      <td>2</td>\n", "      <td>2025-07-24</td>\n", "      <td>1337.21</td>\n", "      <td>2025-07-25</td>\n", "      <td>1337.21</td>\n", "      <td>6</td>\n", "      <td>20</td>\n", "      <td>1</td>\n", "      <td>8dc8e0ca0444a7af2af59c932a4795fcc466a048b1cf2f...</td>\n", "      <td>1</td>\n", "      <td>0.45</td>\n", "      <td>3</td>\n", "      <td>10.0</td>\n", "      <td>7</td>\n", "      <td>24</td>\n", "      <td>24.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1003644 rows × 18 columns</p>\n", "</div>"], "text/plain": ["        data_inclusao  status_boleto data_vencto  vl_boleto   dt_pagto  \\\n", "0          2024-07-01              2  2024-07-22     283.42 2024-07-03   \n", "1          2024-07-01              2  2024-07-22     330.42 2024-07-03   \n", "2          2024-07-03              2  2024-07-22     374.15 2024-07-30   \n", "3          2024-07-03              2  2024-07-22     324.13 2024-07-16   \n", "4          2024-07-03              2  2024-07-22     337.00 2024-07-23   \n", "...               ...            ...         ...        ...        ...   \n", "1003639    2025-06-30              2  2025-07-28   34161.47 2025-07-29   \n", "1003640    2025-06-30              2  2025-07-24    1170.92 2025-07-25   \n", "1003641    2025-06-30              2  2025-07-28      21.78 2025-07-29   \n", "1003642    2025-06-30              2  2025-08-11      21.78 2025-08-12   \n", "1003643    2025-06-30              2  2025-07-24    1337.21 2025-07-25   \n", "\n", "         vl_pagto  banco  qtd_acessos_pagador  pagador_cnpjcpf  \\\n", "0          263.58      8                    2                2   \n", "1          307.29      8                    2                2   \n", "2          382.39      8                    4                2   \n", "3          324.13      8                    0                2   \n", "4          337.00      8                    0                2   \n", "...           ...    ...                  ...              ...   \n", "1003639  34161.47      6                    0                1   \n", "1003640   1170.92      6                    0                1   \n", "1003641     21.78      6                    1                1   \n", "1003642     21.78      6                    1                1   \n", "1003643   1337.21      6                   20                1   \n", "\n", "                                    pagador_inscricao_hash  tipo_juros  juros  \\\n", "0        f5e3d835f189bcdc3c06e012a54a648938e587ecf752a8...           1   0.08   \n", "1        8cafe0ee5d35f8d717a9165a3a9cfd284dd080adc977cd...           1   0.10   \n", "2        fa65b15f453acb990f8492a5b30a58849d0c87985a0a90...           1   0.11   \n", "3        db63183e80b196093f7a06ee4affc35b89daa05849412f...           1   0.10   \n", "4        a0b12a8471aec5c6605a529cf284990656af7385bc5e85...           1   0.10   \n", "...                                                    ...         ...    ...   \n", "1003639  7a149f96b2c656019a5eeda15e46a44b911d4aabb59cba...           1  11.39   \n", "1003640  26b1451db9083ef0e21a93d83d50cd508e2daa484b006e...           1   0.39   \n", "1003641  327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...           1   0.01   \n", "1003642  327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...           1   0.01   \n", "1003643  8dc8e0ca0444a7af2af59c932a4795fcc466a048b1cf2f...           1   0.45   \n", "\n", "         tipo_multa  multa  grupo  pagador_estado  dias_para_vencimento  \\\n", "0                 3    2.0      1              24                  21.0   \n", "1                 3    2.0      1              24                  21.0   \n", "2                 3    2.0      1              24                  19.0   \n", "3                 3    2.0      1              24                  19.0   \n", "4                 3    2.0      1              24                  19.0   \n", "...             ...    ...    ...             ...                   ...   \n", "1003639           3   10.0      7              22                  28.0   \n", "1003640           3   10.0      7              24                  24.0   \n", "1003641           3   10.0      7              24                  28.0   \n", "1003642           3   10.0      7              24                  42.0   \n", "1003643           3   10.0      7              24                  24.0   \n", "\n", "         situacao_pagamento  \n", "0                         1  \n", "1                         1  \n", "2                         2  \n", "3                         1  \n", "4                         2  \n", "...                     ...  \n", "1003639                   2  \n", "1003640                   2  \n", "1003641                   2  \n", "1003642                   2  \n", "1003643                   2  \n", "\n", "[1003644 rows x 18 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(dados_pagos)"]}, {"cell_type": "code", "execution_count": 22, "id": "152e0c8d", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "data_inclusao", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "status_boleto", "rawType": "int8", "type": "integer"}, {"name": "data_vencto", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "vl_boleto", "rawType": "float64", "type": "float"}, {"name": "dt_pagto", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "vl_pagto", "rawType": "float64", "type": "float"}, {"name": "banco", "rawType": "int8", "type": "integer"}, {"name": "qtd_acessos_pagador", "rawType": "int64", "type": "integer"}, {"name": "pagador_cnpjcpf", "rawType": "int8", "type": "integer"}, {"name": "pagador_inscricao_hash", "rawType": "object", "type": "string"}, {"name": "tipo_juros", "rawType": "int8", "type": "integer"}, {"name": "juros", "rawType": "float64", "type": "float"}, {"name": "tipo_multa", "rawType": "int8", "type": "integer"}, {"name": "multa", "rawType": "float64", "type": "float"}, {"name": "grupo", "rawType": "int8", "type": "integer"}, {"name": "pagador_estado", "rawType": "int64", "type": "integer"}, {"name": "dias_para_vencimento", "rawType": "float64", "type": "float"}], "ref": "235e35b1-caa5-42ef-88c9-b209f5e3891a", "rows": [["0", "2025-06-24 00:00:00", "4", "2025-07-30 00:00:00", "360.0", null, null, "8", "0", "2", "99e02f3b1ca56d979ad615782a839450b7dbabb155911046386eb9d34ddaf125", "1", "0.11", "3", "2.0", "1", "24", "36.0"], ["1", "2025-06-27 00:00:00", "4", "2025-07-23 00:00:00", "208.08", null, null, "8", "0", "2", "24d3131aeddadee962b8c6aa27c14e04ba5482743fb76f471819933b73c01a38", "1", "0.06", "3", "2.0", "1", "24", "26.0"], ["2", "2025-06-30 00:00:00", "4", "2025-07-24 00:00:00", "297.02", null, null, "8", "0", "2", "9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbbc3b7ba5bef574a2a04", "1", "0.09", "3", "2.0", "1", "24", "24.0"], ["3", "2025-06-30 00:00:00", "4", "2025-07-29 00:00:00", "297.02", null, null, "8", "0", "2", "9d75dbc691022c487a29db4bbbdd04abc705c31767eff0a10347fac58873801b", "1", "0.09", "3", "2.0", "1", "24", "29.0"], ["4", "2025-06-30 00:00:00", "4", "2025-07-28 00:00:00", "260.55", null, null, "8", "0", "2", "bfd5f75eb99f65b4c61617af3d2a4b4860a74f8e407ed8666571192072cc8fe0", "1", "0.08", "3", "2.0", "1", "24", "28.0"], ["5", "2025-06-30 00:00:00", "4", "2025-07-24 00:00:00", "283.21", null, null, "8", "0", "2", "72cf28cb765b3e0933167d5d093e9228aad60505a881b100573660ab96af0a8f", "1", "0.08", "3", "2.0", "1", "24", "24.0"], ["6", "2025-06-30 00:00:00", "4", "2025-07-24 00:00:00", "252.76", null, null, "8", "0", "2", "3c73f998db00500cb505fa9a63a6fcca8b00554ce3832ce3d57301097f5d30e2", "1", "0.08", "3", "2.0", "1", "24", "24.0"], ["7", "2025-06-30 00:00:00", "4", "2025-07-24 00:00:00", "352.9", null, null, "8", "0", "2", "3e056afc1652b412b1a5cef2625563b8e4c7eb8e5334ef93434915b737572a00", "1", "0.11", "3", "2.0", "1", "24", "24.0"], ["8", "2025-06-30 00:00:00", "4", "2025-07-24 00:00:00", "448.23", null, null, "8", "0", "2", "271586e05b2b3f1f9a59232d9507ca15c5a6fcb7a5f14a426bc8c439be094190", "1", "0.13", "3", "2.0", "1", "24", "24.0"], ["9", "2025-06-30 00:00:00", "4", "2025-07-23 00:00:00", "256.78", null, null, "8", "0", "2", "5116a9e83abeb2920e1ec8c2cefcf92a0a20432b450363dfa910ff39388e9a1e", "1", "0.08", "3", "2.0", "1", "24", "23.0"], ["10", "2025-06-30 00:00:00", "4", "2025-07-24 00:00:00", "333.29", null, null, "8", "0", "2", "aad306f8bcf24dc6091280d238bc6918ab43f80f5b2a37a2d5d783cc912d7bcd", "1", "0.1", "3", "2.0", "1", "24", "24.0"], ["14226", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "337.0", null, null, "8", "0", "2", "81fab7e0ce1bf16c9054cf95d423cc094e698d36f0c4d1e9161ed6596699e655", "1", "0.1", "3", "2.0", "1", "24", "19.0"], ["14227", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "374.35", null, null, "8", "0", "2", "3d371535853c1478faa3472ee8c1505e59657784dceef096e83ee2ce456372f9", "1", "0.11", "3", "2.0", "1", "24", "19.0"], ["14228", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "356.31", null, null, "8", "0", "2", "a61ea21c3fd004aef76aa1aab4ab87f4397835f2c75d7d784ec9b88cf96a5da2", "1", "0.11", "3", "2.0", "1", "24", "19.0"], ["14229", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "340.0", null, null, "8", "0", "2", "87c23bc731ed14f70bcfbf475bbe64a174d210720625fe739c6968a88c69b059", "1", "0.1", "3", "2.0", "1", "24", "19.0"], ["14230", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "281.86", null, null, "8", "2", "2", "543493164f6c955342278aa77525a3fa308794e8bdda247928649e4f423237b6", "1", "0.08", "3", "2.0", "1", "24", "19.0"], ["14231", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "336.73", null, null, "8", "0", "2", "b9a316023262e991d5c13efecb0eef0b539f52818f0d98c12af9c673a6082ff7", "1", "0.1", "3", "2.0", "1", "24", "19.0"], ["14232", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "155.37", null, null, "8", "0", "2", "952ffaa74e5beac97aff3297abbade5734dd51f66b55a077a81b6cf6433f7cb4", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14233", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "387.29", null, null, "8", "0", "2", "d33002455edbad23e646a6ebef78785e639f15a4b9c17edeb7dbac707b2d5a00", "1", "0.12", "3", "2.0", "1", "24", "19.0"], ["14234", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "245.0", null, null, "8", "0", "2", "000dd3ed5a890ddbcdc313a90cb15b3178fa37b80f02c30b951fd4b58a7abe73", "1", "0.07", "3", "2.0", "1", "24", "19.0"], ["14235", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "317.09", null, null, "8", "0", "2", "303a13dcc581a34287226d4a0a0b2e1b438d302e7e3abd5c700022a8803238c8", "1", "0.1", "3", "2.0", "1", "24", "19.0"], ["14236", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "306.8", null, null, "8", "0", "2", "da9f159875c20b1aee8a32bb26042b91b1471912009d765cfa1069d62b8f560c", "1", "0.09", "3", "2.0", "1", "24", "19.0"], ["14237", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "344.22", null, null, "8", "0", "2", "10577d454944b0f8f8d1afa03af72cf68535a8005681dc30868a97d4c26a6f8b", "1", "0.1", "3", "2.0", "1", "24", "19.0"], ["14238", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "306.8", null, null, "8", "0", "2", "34afd9ccb72562e71e3f8b23493b706bd0eb31e77b8ba574a74cb12a01f1de6b", "1", "0.09", "3", "2.0", "1", "24", "19.0"], ["14239", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "387.29", null, null, "8", "0", "2", "74580fab4af5de143a03d5f436210a6a7aca583d2454547e97303a28cb738f57", "1", "0.12", "3", "2.0", "1", "24", "19.0"], ["14240", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "292.5", null, null, "8", "0", "2", "180e326c542ce34842871aa7d85d798c9f0701275fac7ac02c1b87560f426ca4", "1", "0.09", "3", "2.0", "1", "24", "19.0"], ["14241", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "336.73", null, null, "8", "0", "2", "c2487e7fd0f7ce5fc887360c1d48084d45bf2adf78aa3a475ed0e93d926989ba", "1", "0.1", "3", "2.0", "1", "24", "19.0"], ["14242", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "324.13", null, null, "8", "0", "2", "cc3d5fe9c5015942a993db405ec6254efeff298bab88be33c80e66473e9b9867", "1", "0.1", "3", "2.0", "1", "24", "19.0"], ["14243", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "283.42", null, null, "8", "0", "2", "7dae97cbfef31e8e8ef6ef4bd659518ca51dd1e9ce21a7360f30267a0c70cdcd", "1", "0.08", "3", "2.0", "1", "24", "19.0"], ["14244", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "212.56", null, null, "8", "0", "2", "549e401926f32fc7cbcaaeebe6f7197d5638c4ebda4f16da5e3f911ae63a4cb7", "1", "0.06", "3", "2.0", "1", "24", "19.0"], ["14245", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "305.17", null, null, "8", "0", "2", "10718c6259d512582a2c7ef3de4c63f9037e3ecf6b9537b64d31aed596f7c127", "1", "0.09", "3", "2.0", "1", "24", "19.0"], ["14246", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "325.52", null, null, "8", "0", "2", "23195e3dc234d64d797c1fae9bb71dad632b8b39146acf6680694b48650fa793", "1", "0.1", "3", "2.0", "1", "24", "19.0"], ["14247", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "265.7", null, null, "8", "0", "2", "bfc85b477b832f4cee3ee97cc1ad3148204d094efa99f58321ccb671cdc25d6b", "1", "0.08", "3", "2.0", "1", "24", "19.0"], ["14248", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "153.53", null, null, "8", "0", "2", "2f52d24523fc22f5fb21c9b3495288b56073bac10a15f9447330d53453647825", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14249", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "155.37", null, null, "8", "0", "2", "e0c05b9b55fee468dc12fece48b8d2195fd95d198ed95480db3333e19c62dab6", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14250", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "155.37", null, null, "8", "0", "2", "cfc091aba3247841189eee45f4fb72e3eeeb11854fd3e3cd364c8003e9e5b067", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14251", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "155.37", null, null, "8", "0", "2", "982e71a4758c661dfa0ee9e96fd3c7f7f8b7a8ef8fb77b75eb94425f4bc6e82b", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14252", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "155.37", null, null, "8", "0", "2", "d2aacb443d6c9d3ac023979c4993876a7de1a969c6e2e09435fbb4210eae708f", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14253", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "155.37", null, null, "8", "6", "2", "9f1088e3aa79fb1e5a3988679d7648115ee047836749cc894dd748d3a1d1081f", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14254", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "155.37", null, null, "8", "0", "2", "f8ec1ff662c25e272e3f0f5229727c92c16e41cafa2f3a01327d5810fcd79e00", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14255", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "153.53", null, null, "8", "0", "2", "ccbe26e1707d84cc76535f8e0a825470075fb5e6e36ad2f1e5aa0053951a0e96", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14256", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "155.36", null, null, "8", "0", "2", "bce1aef98d2ed4e4003dcfaf7784a35feb612bdb79f79f4d3d89d824c6f57ccc", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14257", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "155.37", null, null, "8", "0", "2", "c5896118bc64a6708334797fbe7dc9b19b7986fce88f1fd9007968a547fb2ef6", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14258", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "155.37", null, null, "8", "0", "2", "80c6a7df84d41608f16719007ca5fadd27bb0344cc425857b71886eea2bc8e84", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14259", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "155.37", null, null, "8", "0", "2", "b32912c7ecec7c79e2c6589d0f3d60b953dd60df16535c2947f1476eb31e17b9", "1", "0.05", "3", "2.0", "1", "24", "19.0"], ["14260", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "265.7", null, null, "8", "0", "2", "392df4fdeba39be41e1fa6205b50fcb0426a27fd1b329f74fdd0eb59f715fb81", "1", "0.08", "3", "2.0", "1", "24", "19.0"], ["14261", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "325.52", null, null, "8", "0", "2", "4aec847cb78caf3371b9180fed5cb14ff179c11d1d1ccce9d9a6b4d101edee0a", "1", "0.1", "3", "2.0", "1", "24", "19.0"], ["14262", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "355.61", null, null, "8", "0", "2", "a6b1b46c6aa4125090309ed26b9b5811a27b97d2cdbba8e53659cb3e5e41f500", "1", "0.11", "3", "2.0", "1", "24", "19.0"], ["14263", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "365.63", null, null, "8", "0", "2", "549e401926f32fc7cbcaaeebe6f7197d5638c4ebda4f16da5e3f911ae63a4cb7", "1", "0.11", "3", "2.0", "1", "24", "19.0"], ["14264", "2024-07-03 00:00:00", "1", "2024-07-22 00:00:00", "305.17", null, null, "8", "2", "2", "392df4fdeba39be41e1fa6205b50fcb0426a27fd1b329f74fdd0eb59f715fb81", "1", "0.09", "3", "2.0", "1", "24", "19.0"]], "shape": {"columns": 17, "rows": 631784}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>data_inclusao</th>\n", "      <th>status_boleto</th>\n", "      <th>data_vencto</th>\n", "      <th>vl_boleto</th>\n", "      <th>dt_pagto</th>\n", "      <th>vl_pagto</th>\n", "      <th>banco</th>\n", "      <th>qtd_acessos_pagador</th>\n", "      <th>pagador_cnpjcpf</th>\n", "      <th>pagador_inscricao_hash</th>\n", "      <th>tipo_juros</th>\n", "      <th>juros</th>\n", "      <th>tipo_multa</th>\n", "      <th>multa</th>\n", "      <th>grupo</th>\n", "      <th>pagador_estado</th>\n", "      <th>dias_para_vencimento</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-06-24</td>\n", "      <td>4</td>\n", "      <td>2025-07-30</td>\n", "      <td>360.00</td>\n", "      <td>NaT</td>\n", "      <td>NaN</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>99e02f3b1ca56d979ad615782a839450b7dbabb1559110...</td>\n", "      <td>1</td>\n", "      <td>0.11</td>\n", "      <td>3</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>36.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-06-27</td>\n", "      <td>4</td>\n", "      <td>2025-07-23</td>\n", "      <td>208.08</td>\n", "      <td>NaT</td>\n", "      <td>NaN</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>24d3131aeddadee962b8c6aa27c14e04ba5482743fb76f...</td>\n", "      <td>1</td>\n", "      <td>0.06</td>\n", "      <td>3</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>26.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-06-30</td>\n", "      <td>4</td>\n", "      <td>2025-07-24</td>\n", "      <td>297.02</td>\n", "      <td>NaT</td>\n", "      <td>NaN</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbb...</td>\n", "      <td>1</td>\n", "      <td>0.09</td>\n", "      <td>3</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-06-30</td>\n", "      <td>4</td>\n", "      <td>2025-07-29</td>\n", "      <td>297.02</td>\n", "      <td>NaT</td>\n", "      <td>NaN</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>9d75dbc691022c487a29db4bbbdd04abc705c31767eff0...</td>\n", "      <td>1</td>\n", "      <td>0.09</td>\n", "      <td>3</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>29.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-06-30</td>\n", "      <td>4</td>\n", "      <td>2025-07-28</td>\n", "      <td>260.55</td>\n", "      <td>NaT</td>\n", "      <td>NaN</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>bfd5f75eb99f65b4c61617af3d2a4b4860a74f8e407ed8...</td>\n", "      <td>1</td>\n", "      <td>0.08</td>\n", "      <td>3</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>28.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1635420</th>\n", "      <td>2025-06-30</td>\n", "      <td>1</td>\n", "      <td>2025-07-28</td>\n", "      <td>57504.45</td>\n", "      <td>NaT</td>\n", "      <td>NaN</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>b516c60bb4f316c26bf4bb87dc710954b61e3c3de469d0...</td>\n", "      <td>1</td>\n", "      <td>19.17</td>\n", "      <td>3</td>\n", "      <td>10.0</td>\n", "      <td>7</td>\n", "      <td>11</td>\n", "      <td>28.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1635422</th>\n", "      <td>2025-06-30</td>\n", "      <td>1</td>\n", "      <td>2025-07-03</td>\n", "      <td>161172.68</td>\n", "      <td>NaT</td>\n", "      <td>NaN</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>5d55a21becbb8e5b9e492021a94abf8c77b18efd7b931a...</td>\n", "      <td>1</td>\n", "      <td>53.72</td>\n", "      <td>3</td>\n", "      <td>10.0</td>\n", "      <td>7</td>\n", "      <td>20</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1635425</th>\n", "      <td>2025-06-30</td>\n", "      <td>4</td>\n", "      <td>2025-08-25</td>\n", "      <td>21.78</td>\n", "      <td>NaT</td>\n", "      <td>NaN</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...</td>\n", "      <td>1</td>\n", "      <td>0.01</td>\n", "      <td>3</td>\n", "      <td>10.0</td>\n", "      <td>7</td>\n", "      <td>24</td>\n", "      <td>56.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1635426</th>\n", "      <td>2025-06-30</td>\n", "      <td>4</td>\n", "      <td>2025-09-09</td>\n", "      <td>21.78</td>\n", "      <td>NaT</td>\n", "      <td>NaN</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...</td>\n", "      <td>1</td>\n", "      <td>0.01</td>\n", "      <td>3</td>\n", "      <td>10.0</td>\n", "      <td>7</td>\n", "      <td>24</td>\n", "      <td>71.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1635427</th>\n", "      <td>2025-06-30</td>\n", "      <td>4</td>\n", "      <td>2025-09-24</td>\n", "      <td>21.80</td>\n", "      <td>NaT</td>\n", "      <td>NaN</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...</td>\n", "      <td>1</td>\n", "      <td>0.01</td>\n", "      <td>3</td>\n", "      <td>10.0</td>\n", "      <td>7</td>\n", "      <td>24</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>631784 rows × 17 columns</p>\n", "</div>"], "text/plain": ["        data_inclusao  status_boleto data_vencto  vl_boleto dt_pagto  \\\n", "0          2025-06-24              4  2025-07-30     360.00      NaT   \n", "1          2025-06-27              4  2025-07-23     208.08      NaT   \n", "2          2025-06-30              4  2025-07-24     297.02      NaT   \n", "3          2025-06-30              4  2025-07-29     297.02      NaT   \n", "4          2025-06-30              4  2025-07-28     260.55      NaT   \n", "...               ...            ...         ...        ...      ...   \n", "1635420    2025-06-30              1  2025-07-28   57504.45      NaT   \n", "1635422    2025-06-30              1  2025-07-03  161172.68      NaT   \n", "1635425    2025-06-30              4  2025-08-25      21.78      NaT   \n", "1635426    2025-06-30              4  2025-09-09      21.78      NaT   \n", "1635427    2025-06-30              4  2025-09-24      21.80      NaT   \n", "\n", "         vl_pagto  banco  qtd_acessos_pagador  pagador_cnpjcpf  \\\n", "0             NaN      8                    0                2   \n", "1             NaN      8                    0                2   \n", "2             NaN      8                    0                2   \n", "3             NaN      8                    0                2   \n", "4             NaN      8                    0                2   \n", "...           ...    ...                  ...              ...   \n", "1635420       NaN      6                    0                1   \n", "1635422       NaN      6                    0                1   \n", "1635425       NaN      6                    0                1   \n", "1635426       NaN      6                    0                1   \n", "1635427       NaN      6                    0                1   \n", "\n", "                                    pagador_inscricao_hash  tipo_juros  juros  \\\n", "0        99e02f3b1ca56d979ad615782a839450b7dbabb1559110...           1   0.11   \n", "1        24d3131aeddadee962b8c6aa27c14e04ba5482743fb76f...           1   0.06   \n", "2        9154dc2de0d3c88cdd77d40a59c2bd77a543fc8dc2ddbb...           1   0.09   \n", "3        9d75dbc691022c487a29db4bbbdd04abc705c31767eff0...           1   0.09   \n", "4        bfd5f75eb99f65b4c61617af3d2a4b4860a74f8e407ed8...           1   0.08   \n", "...                                                    ...         ...    ...   \n", "1635420  b516c60bb4f316c26bf4bb87dc710954b61e3c3de469d0...           1  19.17   \n", "1635422  5d55a21becbb8e5b9e492021a94abf8c77b18efd7b931a...           1  53.72   \n", "1635425  327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...           1   0.01   \n", "1635426  327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...           1   0.01   \n", "1635427  327c39f3fc0de14a6096a4980e2b4be9a8014e0db69b65...           1   0.01   \n", "\n", "         tipo_multa  multa  grupo  pagador_estado  dias_para_vencimento  \n", "0                 3    2.0      1              24                  36.0  \n", "1                 3    2.0      1              24                  26.0  \n", "2                 3    2.0      1              24                  24.0  \n", "3                 3    2.0      1              24                  29.0  \n", "4                 3    2.0      1              24                  28.0  \n", "...             ...    ...    ...             ...                   ...  \n", "1635420           3   10.0      7              11                  28.0  \n", "1635422           3   10.0      7              20                   3.0  \n", "1635425           3   10.0      7              24                  56.0  \n", "1635426           3   10.0      7              24                  71.0  \n", "1635427           3   10.0      7              24                  86.0  \n", "\n", "[631784 rows x 17 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(dados_em_aberto)"]}, {"cell_type": "markdown", "id": "fa06606c", "metadata": {}, "source": ["hipótese: O comportamento de pagamento é influenciado pelo grupo financeiro ao qual o pagador pertence."]}, {"cell_type": "code", "execution_count": 23, "id": "16ca3fc6", "metadata": {}, "outputs": [], "source": ["# Confirme que temos a coluna de diferença de datas\n", "dados_pagos['dias_atraso'] = (dados_pagos['dt_pagto'] - dados_pagos['data_vencto']).dt.days"]}, {"cell_type": "code", "execution_count": 24, "id": "662506ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📉 Média de atraso (dias) por grupo (negativo = adiantado):\n", "grupo\n", "1   -4.562997\n", "5   -2.987431\n", "7   -2.071622\n", "3    0.297836\n", "2    1.023043\n", "4    1.270217\n", "6    1.592826\n", "Name: dias_atraso, dtype: float64\n"]}], "source": ["atraso_medio = dados_pagos.groupby('grupo')['dias_atraso'].mean().sort_values()\n", "print(\"📉 Média de atraso (dias) por grupo (negativo = adiantado):\")\n", "print(atraso_medio)"]}, {"cell_type": "code", "execution_count": 25, "id": "085ddcbb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "✅ % de pagamentos adiantados por grupo:\n", "grupo\n", "1    0.536053\n", "5    0.341616\n", "3    0.182757\n", "7    0.124491\n", "6    0.110642\n", "4    0.098741\n", "2    0.060850\n", "dtype: float64\n"]}], "source": ["adiantado = dados_pagos[dados_pagos['dias_atraso'] < 0]\n", "pct_adiantado = adiantado.groupby('grupo').size() / dados_pagos.groupby('grupo').size()\n", "pct_adiantado = pct_adiantado.sort_values(ascending=False)\n", "\n", "print(\"\\n✅ % de pagamentos adiantados por grupo:\")\n", "print(pct_adiantado)"]}, {"cell_type": "code", "execution_count": 29, "id": "14d0ed85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["grupo\n", "5    0.010964\n", "7    0.007716\n", "6    0.006667\n", "3    0.004887\n", "4    0.002547\n", "2    0.000559\n", "1    0.000000\n", "dtype: float64\n"]}], "source": ["# Número de pagos com atraso > 30 dias por grupo\n", "inadimplente = dados_pagos[dados_pagos['dias_atraso'] > 30]\n", "num_inadimplentes = inadimplente.groupby('grupo').size()\n", "\n", "# Total de pagos por grupo\n", "total_pagos = dados_pagos.groupby('grupo').size()\n", "\n", "# Garan<PERSON>r que todos os grupos apareçam no numerador\n", "num_inadimplentes = num_inadimplentes.reindex(total_pagos.index, fill_value=0)\n", "\n", "# Agora calcular a porcentagem\n", "pct_inadimplente = (num_inadimplentes / total_pagos).sort_values(ascending=False)\n", "\n", "print(pct_inadimplente)"]}, {"cell_type": "code", "execution_count": 32, "id": "adc1ccc0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total de boletos pagos no grupo 1: 14215\n", "Quantidade de inadimplentes (>30 dias de atraso) no grupo 1: 0\n", "<PERSON>or atraso no grupo 1: 23 dias\n"]}], "source": ["# Filtrar boletos pagos do grupo 1\n", "grupo1_pagos = dados_pagos[dados_pagos['grupo'] == 1]\n", "\n", "print(f\"Total de boletos pagos no grupo 1: {len(grupo1_pagos)}\")\n", "\n", "# Filtrar os que têm mais de 30 dias de atraso\n", "grupo1_inadimplente = grupo1_pagos[grupo1_pagos['dias_atraso'] > 30]\n", "\n", "print(f\"Quantidade de inadimplentes (>30 dias de atraso) no grupo 1: {len(grupo1_inadimplente)}\")\n", "\n", "# Maior atraso registrado no grupo 1\n", "max_atraso = grupo1_pagos['dias_atraso'].max()\n", "\n", "print(f\"Maior atraso no grupo 1: {max_atraso} dias\")"]}, {"cell_type": "code", "execution_count": 27, "id": "3b976771", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "💰 Valor médio pago por grupo:\n", "grupo\n", "2    11490.575148\n", "7     7444.649080\n", "3     5022.769176\n", "4     4695.527585\n", "5     3018.376910\n", "6      788.943711\n", "1      310.097700\n", "Name: vl_pagto, dtype: float64\n"]}], "source": ["valor_medio = dados_pagos.groupby('grupo')['vl_pagto'].mean().sort_values(ascending=False)\n", "print(\"\\n💰 Valor médio pago por grupo:\")\n", "print(valor_medio)"]}, {"cell_type": "code", "execution_count": 28, "id": "e4a69d3c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Exemplo: atraso médio por grupo\n", "atraso_medio.plot(kind='bar', title='Atraso médio por grupo (dias)', color='skyblue')\n", "plt.ylabel('Dias (negativo = adiantado)')\n", "plt.xlabel('Grupo')\n", "plt.axhline(0, color='red', linestyle='--', linewidth=1)\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "d7754707", "metadata": {}, "source": ["Grupo 1: <PERSON><PERSON>\n", "* Paga em média 4.56 dias antes do vencimento\n", "* 53.6% dos pagamentos são adiantados\n", "* Zero inadimplência com atraso >30 dias\n", "* Valor médio pago: R$ 310,10 (menor valor, mas mais pontual)\n", "* Comportamento: muito adimplente, paga cedo, com pouca tolerância para atrasos\n", "\n", "Grupos 5, 7, 3: Comportamento intermediário\n", "* Pago em média entre 2.07 dias antes (grupo 7) e +0.30 dias após (grupo 3)\n", "* % adiantados entre 12.4% (grupo 7) e 34.2% (grupo 5)\n", "* Inadimplência baixa (entre 0.49% e 1.10%)\n", "* Comportamento: regular, com alguns adiantados e outros dentro do prazo\n", "\n", "Grupos 2, 4, 6: <PERSON><PERSON>\n", "* Atraso médio entre ***** (grupo 2) e ***** dias (grupo 6)\n", "* % adiantados abaixo de 12%\n", "* Grupo 6 tem 0.67% de inadimplência >30 dias (maior entre todos)\n", "* Grupo 2 paga o maior valor médio (R$ 11.490,58), mas com maior atraso\n", "* Comportamento: mais propenso a atrasar, com risco de inadimplência"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}