# Documentação Modelo Preditivo - Inteli

```
INSTRUÇÕES GERAIS (remova este trecho ao final)

Você deve editar este documento utilizando notação markdown - siga as convenções neste link
https://docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax
```

## Fint-li

### Ceraza

* Arthur <PERSON> Almeida
* Francisco de Araújo <PERSON>rre<PERSON>l<PERSON>
* <PERSON>uilherme <PERSON>
* <PERSON><PERSON>
* Maria Vitória dos Santos
* <PERSON><PERSON>
* <PERSON><PERSON><PERSON>

## Sumário

[1. Introdução](#c1)

[2. Objetivos e Justificativa](#c2)

[3. Metodologia](#c3)

[4. Desenvolvimento e Resultados](#c4)

[5. Conclusões e Recomendações](#c5)

[6. Referências](#c6)

[Anexos](#attachments)

## <a name="c1"></a>1. Introdução

&emsp; A Finnet Tecnologia é uma empresa de médio porte, sediada em São Paulo, que atua no setor de tecnologia financeira B2B (techfin). Com mais de duas décadas de atuação, a empresa se consolidou no mercado nacional como referência em automação de processos financeiros empresariais, conectando corporações a instituições financeiras por meio de soluções digitais integradas.

&emsp; Seu principal foco está em otimizar rotinas de contas a pagar, contas a receber, fluxo de caixa e conciliação bancária, com produtos voltados especialmente para médias e grandes empresas. Entre suas soluções, destaca-se a plataforma Luna, voltada à gestão de cobranças e recebíveis.

&emsp; Atualmente, a empresa busca compreender um problema recorrente enfrentado por seus clientes: a dificuldade em lidar com altos índices de inadimplência no mercado nacional. Em particular, gestores financeiros têm enfrentado desafios para entender a tendência de pagamento de seus clientes, prever o percentual de inadimplência em determinados períodos e estimar o valor que será efetivamente recebido. Esses pontos críticos motivam o desenvolvimento do presente projeto em parceria com o Instituto de Tecnologia e Liderança (Inteli).

## <a name="c2"></a>2. Objetivos e Justificativa

### 2.1 Objetivos

```
Descreva resumidamente os objetivos gerais e específicos do seu parceiro de negócios.

Remova este bloco ao final
```

### 2.2 Proposta de solução

```
Descreva resumidamente sua proposta de modelo preditivo e como esse modelo pretende resolver o problema, atendendo os objetivos.

Remova este bloco ao final
```

### 2.3 Justificativa

```
Faça uma breve defesa de sua proposta de solução, escreva sobre seus potenciais, seus benefícios e como ela se diferencia.

Remova este bloco ao final
```

## <a name="c3"></a>3. Metodologia

```
Descreva a metodologia CRISP-DM e suas etapas de desenvolvimento, citando o referencial teórico. Você deve apenas enunciar os métodos, sem dizer ainda como eles foram aplicados, nem quais resultados foram obtidos.

Remova este bloco ao final
```

## <a name="c4"></a>4. Desenvolvimento e Resultados

### 4.1. Compreensão do Problema


#### 4.1.1. Contexto da indústria

&emsp; Abaixo, apresentamos um panorama geral do setor, iniciando pelo mapeamento dos principais concorrentes, seguido pela definição do modelo de negócio da Finnet, as tendências que vêm transformando o segmento e, por fim, a análise das 5 Forças de Porter, que nos ajudará a compreender melhor os desafios e oportunidades estratégicas.

**Principais concorrentes**

&emsp; No segmento de automação financeira B2B, destacam-se três plataformas consolidadas: a **Nexxera**, com mais de 30 anos de mercado, que oferece conciliação bancária multibanco, integração multicanal em tempo real e módulos de supply chain finance; a **Accesstage (Veragi)**, portal que unifica Contas a Pagar, Contas a Receber e Tesouraria, incluindo emissão e processamento de boletos, conciliação de cartões e antecipação de recebíveis; e a **TOTVS Techfin**, unidade joint-venture da TOTVS com o Itaú, que traz o conceito de “ERP Banking” ao permitir operações financeiras (pagamentos, cobranças, crédito) diretamente dentro do ERP, eliminando fricções entre sistemas de gestão e internet banking.

**Modelo de negócio da Finnet (plataforma Luna)**

&emsp; A Luna opera no modelo **B2B SaaS + transacional**, com assinatura mensal pelos módulos de emissão de cobranças (boleto, PIX, cartão), conciliação automática e relatórios analíticos. Sobre cada transação — emissão ou conciliação — é cobrada uma tarifa variável, enquanto contratos corporativos de maior porte podem incluir SLAs de processamento em tempo real, integrações customizadas e dashboards avançados.


**Tendências do setor**

1. **Open Finance/Open Banking**: expansão de APIs padronizadas que aceleram integrações entre ERPs, techfins e bancos;
2. **Pagamentos instantâneos (PIX, ISO 20022)**: demanda por infraestrutura capaz de liquidar transações em milissegundos e notificar eventos em tempo real;
3. **Embedded finance e “ERP Banking”**: incorporação de serviços financeiros — conta digital, antecipação de recebíveis, linhas de crédito B2B — dentro de sistemas de gestão;
4. **Analytics preditivo e IA**: adoção crescente de machine learning para scoring de inadimplência, detecção de fraudes e recomendação de janelas de pagamento.

<br>

**Análise das 5 Forças de Porter**

A análise das Cinco Forças de Porter permite avaliar o cenário competitivo do setor de soluções financeiras baseadas em Open Banking, identificando barreiras, pressões e oportunidades estratégicas. Este mapeamento orienta decisões para ganho de vantagem competitiva e posicionamento no mercado.

| Força                                    | Impacto  | Descrição                                                                                                                                                           |
| ---------------------------------------- | -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Ameaça de novos entrantes**            | Moderada | Open Banking facilita entry points técnicos, mas exigências regulatórias (homologação BC, certificações) e altos custos de compliance elevam a barreira de entrada. |
| **Poder de negociação dos fornecedores** | Alto     | Bancos e processadoras detêm infraestrutura crítica (conectores, certificações, data centers), podendo impor preços e prazos de integração.                         |
| **Poder de negociação dos compradores**  | Alto     | Grandes empresas negociam tarifas, SLAs e customizações; há facilidade de migração entre plataformas SaaS em caso de insatisfação.                                  |
| **Ameaça de produtos substitutos**       | Moderada | Soluções internas e ERPs genéricos continuam em uso, mas perdem para techfins em agilidade, automação e *insights* preditivos.                                        |
| **Rivalidade entre concorrentes**        | Intensa  | Fintechs e techfins competem por API-first, UX e analytics avançado; diferenciação via amplitude de integrações bancárias e profundidade analítica é crucial.       |

O setor apresenta alta rivalidade e pressões significativas de fornecedores e clientes, exigindo diferenciação contínua e eficiência operacional. As empresas que explorarem integrações amplas e análises preditivas terão maior potencial de liderança.

---

####  4.1.2. Análise SWOT do setor de cobranças da Finnet (Plataforma Luna) 

&emsp; A análise SWOT (Strengths, Weaknesses, Opportunities e Threats) é um método de planejamento estratégico que engloba o estudo das forças, fraquezas, oportunidades e ameaças que uma empresa enfrenta. Ela serve para ajudar empresas e pessoas a tomarem decisões estratégicas com base em uma avaliação do ambiente interno (forças e fraquezas) e externo (oportunidades e ameaças).



####  4.1.2. Análise SWOT do setor de cobranças da Finnet (Plataforma Luna) 

&emsp; A análise SWOT (Strengths, Weaknesses, Opportunities e Threats) é um método de planejamento estratégico que engloba o estudo das forças, fraquezas, oportunidades e ameaças que uma empresa enfrenta. Ela serve para ajudar empresas e pessoas a tomarem decisões estratégicas com base em uma avaliação do ambiente interno (forças e fraquezas) e externo (oportunidades e ameaças).

---

<div align="center">
  
  <sub>Figura 1 - Análise SWOT</sub>  
  
  <img src="../assets/negocios/analise_swot.png" alt="Imagem da Análise SWOT" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza)</sup>

</div> 

<br>

---

*Esta análise se concentra no setor de cobranças da Finnet Tecnologia, com foco em sua plataforma online de automação e gestão, a Luna.*

DIÁRIO DO COMÉRCIO. Setor de cobranças e recebíveis ganha aliado. 2022. Disponível em: https://diariodocomercio.com.br/negocios/setor-de-cobrancas-e-recebiveis-ganha-aliado/. Acesso em: 06 ago. 2025.

#### **Forças (Strengths)**
* **Liberdade de negociação de taxas:** Diferente de muitas plataformas de pagamento que atuam como intermediadoras e "obrigam" o cliente a usar suas taxas de processamento, a Luna permite que a empresa utilize as taxas que ela já negociou diretamente com seus próprios bancos e adquirentes. Ou seja, a empresa não fica refém das taxas impostas pela plataforma, aumentando o poder de barganha dos clientes.
* **Solução multibancos:** A plataforma consegue centralizar e padronizar o protocolo de comunicação com mais de 150 instituições financeiras. Para uma grande empresa, isso significa que ela não precisa lidar com múltiplos sistemas e formatos de arquivo para cada banco, simplificando a gestão e garantindo uma visão unificada do fluxo de caixa e das cobranças.
* **API de integração robusta:** A Finnet se destaca por sua densa API de integração, que é o coração da sua solução de conectividade financeira. Isso permite que a plataforma Luna se conecte de forma fluida com sistemas de gestão (ERPs), CRMs e outras ferramentas de automação já utilizadas pelos clientes. A principal força dessa API é: automação e Eficiência , flexibilidade e Segurança
---
#### **Fraquezas (Weaknesses)**
* **Custo e competitividade de preço:** Embora a Luna ofereça um conjunto robusto de funcionalidades, o custo de implementação e o preço da própria plataforma podem ser uma barreira. Concorrentes que oferecem soluções mais simples e com preços mais acessíveis podem atrair empresas que buscam uma solução mais rápida ou com menor investimento inicial.
* **Dependência do ecossistema de parceiros:** A ampla rede de conexões da Finnet é uma força, mas também pode ser uma fraqueza. Se um cliente ou parceiro de negócios utilizar uma instituição financeira que não está homologada na plataforma, a integração se torna complexa, o que pode limitar a flexibilidade da Luna e a satisfação do cliente.
* **Limitação do foco no B2B:** O foco no mercado de grandes empresas, apesar de garantir expertise em um nicho, também restringe o mercado potencial da Finnet. A empresa pode ser mais vulnerável a crises econômicas que afetem o setor corporativo e pode ter dificuldade em competir com players que atendem a um público mais amplo.
LUNAPAY. Plataforma Luna – Integração e automação financeira. 2022. 
Disponível em: https://institucional.lunapay.com.br/. Acesso em: 06 ago. 2025.
---
#### **Oportunidades (Opportunities)**
* **Adoção de Inteligência Artificial:** A Finnet pode ir além da automação e usar a IA para criar modelos preditivos de inadimplência, otimizar estratégias de cobrança e até mesmo sugerir o meio de pagamento ideal para cada cliente, maximizando as taxas de recebimento.
* **Expansão em estratégias de cobrança:** A Luna tem a oportunidade de oferecer uma abordagem mais humanizada e multicanal. Utilizando dados da plataforma, a Finnet pode ajudar os clientes a criar réguas de cobrança personalizadas, usando canais como WhatsApp, e-mail e SMS, o que melhora a experiência e a eficiência da cobrança.
* **Pagamentos internacionais:** A Finnet pode expandir a Luna para simplificar as complexidades dos pagamentos internacionais B2B, como câmbio e regulamentação. Isso permitiria que os clientes gerenciassem pagamentos e recebimentos de e para o exterior em uma única plataforma.
---
#### **Ameaças (Threats)**
* **Evolução regulatória no Brasil:** O rápido avanço das regulamentações brasileiras no setor financeiro, como as mudanças no Banco Central e a Lei Geral de Proteção de Dados (LGPD), pode exigir investimentos significativos e rápidos em tecnologia e compliance, o que representa um desafio e um risco para a Finnet.
* **Risco de segurança e reputação:** No setor financeiro, a confiança é fundamental. Qualquer incidente de segurança cibernética ou uma falha prolongada no serviço pode resultar em multas regulatórias, perda de clientes e um dano irreparável à reputação da empresa.
* **Dinâmica do mercado de tecnologia:** O mercado de tecnologia financeira é extremamente dinâmico. O que é inovador hoje pode se tornar obsoleto amanhã. A Finnet precisa se manter em constante inovação para não ser ultrapassada por concorrentes mais ágeis ou por novas tecnologias emergentes, como o avanço do Pix e do Open Finance.
TECFLOW. Finnet lança solução de cobrança com integração multibanco. 2022. Disponível em: https://tecflow.com.br/2022/09/29/finnet-cobrancas-taxas-bancos/. Acesso em: 06 ago. 2025.

---

#### 4.1.3. Planejamento Geral da Solução

```
a) quais os dados disponíveis (fonte e conteúdo - exemplo: dados da área de Compras da empresa descrevendo seus fornecedores).
b) qual a solução proposta (pode ser um resumo do texto da Seção 2.2).
c) como a solução proposta deverá ser utilizada.
d) quais os benefícios trazidos pela solução proposta.
e) qual será o critério de sucesso.
```

#### 4.1.4. Value Proposition Canvas



&emsp; Esta seção apresenta o planejamento geral da solução que será desenvolvida, focando no desenvolvimento de uma ferramenta preditiva e analítica para apoiar a gestão de processos financeiros. O objetivo é facilitar a administração financeira de pagamentos, tornando os processos mais rápidos, seguros e eficientes no uso dos dados e nas operações bancárias.

&emsp; Os dados disponíveis para esta solução vêm das planilhas fornecidas pela Finnet, que contêm informações financeiras essenciais para a empresa. As informações fornecidas são:

| Título do Dado           | Descrição                                         |
|--------------------------|--------------------------------------------------|
| id_grupo                 | Identificador do grupo ao qual o registro pertence |
| id_beneficiario          | Identificador da filial vinvulada aos grupos       |
| Numero_do_boleto         | Número único do boleto gerado                      |
| data_inclusao            | Data em que o registro foi incluído no sistema    |
| status_boleto            | Situação atual do boleto (ex: pago, pendente)     |
| data_vencto              | Data de vencimento do boleto                        |
| vl_boleto                | Valor total do boleto                               |
| dt_pagto                 | Data em que o pagamento foi efetuado               |
| vl_pagto                 | Valor que foi efetivamente pago                     |
| banco                    | Banco onde o pagamento foi realizado     |
| id_pagador               | Identificador do pagador do boleto                  |
| pagador_cep              | CEP do endereço do pagador                          |
| pagador_cidade           | Cidade do pagador                                   |
| qtd_acessos_pagador      | Número de vezes que o pagador acessou o sistema    |
| pagador_dt_ultimo_acesso | Data do último acesso realizado pelo pagador       |
| pagador_cnpjcpf          | Identifica se o pagador é registrado por CPF ou por CNPJ                              |


&emsp; Essas informações são fundamentais para que o sistema possa realizar *insights* dos processos financeiros.

&emsp; A solução proposta envolve o desenvolvimento de um sistema que utiliza os dados financeiros disponíveis para construir um modelo preditivo capaz de antecipar comportamentos financeiros, como inadimplência, riscos de atraso e projeções de fluxo de caixa. Com base na análise dos dados históricos, o sistema fornecerá *insights* que ajudarão o cliente a tomar decisões financeiras mais informadas e proativas. Essa abordagem visa aumentar a organização financeira dos beneficiários da Finnet.

&emsp; A solução será utilizada pelas equipes financeiras e administrativas para monitorar e prever comportamentos financeiros com base na análise de dados históricos. Com isso, será possível identificar antecipadamente atrasos, inadimplências e variações no fluxo de caixa, permitindo agir de forma proativa para corrigir ou prevenir problemas.

&emsp; Além disso, ao automatizar a análise preditiva, reduzirá o esforço manual e a chance de erros, aumentando a eficiência operacional. Asiim, a empresa poderá tomar decisões mais estratégicas, otimizar a alocação de recursos e manter a saúde financeira de forma consistente.

&emsp; O critério de sucesso da solução será alcançado quando o modelo preditivo apresentar uma acurácia satisfatória na previsão dos comportamentos financeiros, como atrasos e falta de pagamentos, facilitando a tomada de decisões eficazes. Também se espera que o sistema contribua para a redução nos casos de inadimplência e melhore o planejamento do fluxo de caixa. Outro indicador de sucesso será a aceitação e facilidade de uso do sistema pelos usuários finais, garantindo que as equipes financeiras adotem a solução no dia a dia.

&emsp; Em resumo, a solução planejada visa transformar os dados financeiros disponíveis em informações valiosas por meio de um modelo preditivo, permitindo que a empresa antecipe riscos e otimize sua gestão financeira. Com a implementação desta solução, espera-se alcançar maior eficiência, segurança e controle, promovendo decisões mais informadas e estratégicas para o sucesso do negócio.

---

####  4.1.4. Canvas da proposta de valor

O **Canvas da Proposta de Valor** é uma ferramenta estratégica que auxilia empresas a desenvolverem produtos e serviços que realmente atendam às necessidades dos seus clientes. Criado por Alexander Osterwalder, o modelo é aprofundado em seu livro *Business Model Generation: Inovação em Modelos de Negócios*.

&emsp; O **Canvas da Proposta de Valor** é uma ferramenta estratégica que auxilia empresas a desenvolverem produtos e serviços que realmente atendam às necessidades dos seus clientes. Criado por Alexander Osterwalder, o modelo é aprofundado em seu livro *Business Model Generation: Inovação em Modelos de Negócios*.


---

#### O modelo é dividido em dois blocos principais:

#### ▸ Perfil do Cliente

- **Tarefas** que o cliente precisa realizar  
- **Dores**: dificuldades, riscos e obstáculos enfrentados  
- **Ganhos** esperados ao utilizar uma solução

#### ▸ Mapa de Valor

- Como o produto/serviço proposto busca:
  - **Aliviar as dores**
  - **Gerar os ganhos**
  - **Atender às necessidades** do cliente através de funcionalidades específicas

---

<br>

<div align="center">
  
  <sub>Figura 2 - Canvas da proposta de valor</sub>  
  
  <img src="../assets/negocios/canvas.png" alt="Imagem do Canvas da Proposta de Valor" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza)</sup>

</div> 

<br>

---

#### Aplicação no Projeto com a Finnet

&emsp; No contexto do nosso projeto com a Finnet, o VPC nos ajudou a entender como agregar valor à solução de gestão de cobranças, oferecendo funcionalidades que apoiam o gestor financeiro na prevenção e análise de inadimplência, um desafio cada vez mais urgente nas médias e grandes empresas do mercado nacional.

SEBRAE. Canvas da proposta de valor. Curitiba: Sebrae/PR, [s.d.]. Disponível em: https://www.sebraepr.com.br/wp-content/uploads/CANVAS-DA-PROPOSTA-DE-VALOR-2.pdf. Acesso em: 5 ago. 2025.

---

#### 4.1.5. Matriz de Riscos


&emsp;  A **matriz de risco** é uma ferramenta essencial no gerenciamento de projetos, utilizada para organizar e visualizar riscos e oportunidades ao longo do desenvolvimento. Ela cruza dois fatores principais:

- **Probabilidade de ocorrência**
- **Impacto potencial sobre o projeto**

&emsp;  Esse modelo, recomendado pelo Project Management Institute (PMI), ajuda a equipe a visualizar e priorizar os riscos e oportunidades mais importantes para o projeto.

---

<br>

<div align="center">
  
  <sub>Figura 03 - Matriz de risco</sub>  
  <br>
  <br>
  <img src="../assets/negocios/matriz_de_risco.png" alt="Imagem da Matriz de risco" width="100%">
  <br>
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza)</sup>

</div>

---

##### Aplicação no projeto com a Finnet

&emsp;  No projeto com a Finnet, que atua em automação financeira para médias e grandes empresas, essa análise de riscos é fundamental para antecipar desafios relacionados à qualidade dos dados e à implementação da solução preditiva de inadimplência. Assim, a equipe assegura a entrega de insights que ajudam os gestores financeiros a tomar decisões mais estratégicas, agregando valor ao produto.

---

#### 4.1.6. Personas

&emsp; Segundo Cooper, Reimann e Cronin (2014), personas são representações semifictícias que ajudam a compreender os usuários e orientar decisões de design. Elas são construídas a partir de dados e características reais do público-alvo, trazendo um entendimento mais claro sobre seus objetivos, necessidades e dores. Isso é essencial para direcionar o desenvolvimento do projeto, garantindo que as funcionalidades atendam às expectativas e problemas reais dos usuários.

COOPER, Alan; REIMANN, Robert; CRONIN, Dave. About Face: The Essentials of Interaction Design. 4. ed. Indianapolis: Wiley, 2014.

<br>

<div align="center">
  
  <sub>Figura 04 - Persona 01</sub>  

  <img src="../assets/personas/persona1.png" alt="Imagem da Persona 1" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza)</sup>

</div>
<br>

&emsp; André representa a perspectiva empresarial de grande porte. Como diretor financeiro de uma das maiores redes varejistas do Brasil, ele enfrenta desafios no gerenciamento de grandes volumes de débitos e precisa de informações rápidas e assertivas para tomar decisões estratégicas. Sua persona evidencia a necessidade de previsibilidade e controle sobre o fluxo financeiro da empresa.

COMPANHIA BRASILEIRA DE DISTRIBUIÇÃO. Pão de Açúcar - Quem Somos. São Paulo, 2025. Disponível em: https://www.paodeacucar.com/. Acesso em: 5 ago. 2025.

<div align="center">
  
  <sub>Figura 05 - Persona 2</sub>  

  <img src="../assets/personas/persona2.png" alt="Imagem da Persona 2" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza)</sup>

</div>
<br>

&emsp; Cristina traz a visão do setor bancário, onde a inadimplência impacta diretamente a situação financeira da instituição. Como gerente de crédito, ela busca soluções que ajudem a priorizar cobranças, prever comportamentos de pagamento e reduzir riscos. Sua persona mostra como nosso projeto pode otimizar processos internos, aumentar a eficiência na comunicação com empresas e melhorar o fluxo de caixa.

ITAÚ UNIBANCO HOLDING S.A. Sobre o Itaú - Institucional. São Paulo, 2025. Disponível em: https://www.itau.com.br/institucional/. Acesso em: 6 ago. 2025.

<div align="center">
  
  <sub>Figura 06 - Persona 3</sub>  

  <img src="../assets/personas/persona3.png" alt="Imagem da Persona 3" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza)</sup>

</div>
<br>

&emsp; Rebeca representa o perfil profissional operacional, que atua diretamente no dia a dia das cobranças e análises financeiras. Sua principal necessidade é ganhar agilidade, confiabilidade e precisão na geração de relatórios para apoiar decisões estratégicas. Ela evidencia a importância de dashboards intuitivos e dados processados automaticamente, eliminando retrabalho e otimizando sua produtividade.

TELEFÔNICA BRASIL S.A. Vivo - Quem Somos. São Paulo, 2025. Disponível em: https://www.vivo.com.br/sobre. Acesso em: 6 ago. 2025.

&emsp; As personas desenvolvidas representam diferentes níveis de atuação no ecossistema financeiro: empresas de grande porte, instituições bancárias e profissionais responsáveis pela análise e execução. Ao considerar esses perfis durante todo o desenvolvimento, aumentamos a chance de entregar um produto que gera valor real para todos os envolvidos, fortalecendo a proposta de inovação da Finnet.

---

#### 4.1.7. Jornadas do Usuário


&emsp;Kalbach (2020) explica que o mapa de jornada do usuário é uma ferramenta de design centrada no usuário que busca representar graficamente as etapas, interações, emoções e pontos de contato vividos por uma persona ao se relacionar com determinado produto ou serviço. A partir desse mapeamento, é possível identificar gargalos, oportunidades de melhoria e momentos-chave de decisão que influenciam a experiência do usuário.

&emsp;No contexto do projeto com a Finnet, a adoção de journey maps auxilia diretamente na compreensão das dores ligadas à inadimplência e ao processo de cobrança, permitindo que o modelo preditivo e as soluções de análise de dados sejam pensados não apenas sob a ótica técnica, mas também com foco nas reais necessidades e expectativas dos diferentes perfis de stakeholders. Dessa forma, o uso dessa tática garante maior alinhamento entre o que o sistema entrega e os problemas estratégicos enfrentados pelas empresas, bancos e profissionais operacionais que lidam diariamente com fluxos financeiros.


<div align="center">
  
  <sub> Mapa do Usuário - André </sub>

  <img src="../assets/jornada/jornada2.jpg" alt="Imagem da jornada do usuário" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza)</sup>

</div>

#### Diretor Financeiro (Persona André)

&emsp; O mapa da jornada do André, diretor financeiro do Pão de Açúcar, fica evidente a tensão constante em lidar com débitos inesperados que comprometem a saúde financeira da empresa. Sua jornada mostra a frustração diante da imprevisibilidade do caixa, mas também a expectativa por uma solução capaz de organizar relatórios, antecipar riscos e oferecer segurança estratégica. A experiência dele revela como a previsibilidade não é apenas uma questão operacional, mas um requisito essencial para garantir a flexibilidade financeira.

<div align="center">
  
  <sub> Mapa do Usuário - Cristina </sub>

  <img src="../assets/jornada/jornada.png" alt="Imagem da jornada do usuário" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza)</sup>

</div> 

#### Gerente de Crédito Bancário (Persona Cristina)

O mapa da jornada da Cristina, gerente de crédito no Itaú, expõe a necessidade de priorização em um ambiente de alta pressão. Ao longo de sua jornada, ela busca entender quais clientes representam maior risco, enfrenta insegurança sobre a confiabilidade dos dados e precisa tomar decisões rápidas para manter o fluxo de caixa saudável. A síntese dessa trajetória mostra que sua principal dor está em equilibrar eficiência e assertividade, e que dashboards claros e dados preditivos se tornam aliados indispensáveis na definição de estratégias de cobrança.
 

<div align="center">
  
  <sub> Mapa do Usuário - Rebeca </sub>

  <img src="../assets/jornada/jornada3.jpg" alt="Imagem da jornada do usuário" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza)</sup>

</div>

#### Analista Operacional (Persona Rebeca)

O mapa da jornada da Rebeca, analista financeira da Finnet, revela o peso do retrabalho e das planilhas pouco automatizadas em sua rotina. A jornada dela demonstra como a repetição de tarefas manuais limita a produtividade e como a adoção de ferramentas inteligentes pode transformar esse cenário, trazendo mais agilidade, redução de erros e liberdade para análises estratégicas. Sua síntese destaca que, mais do que otimizar processos, a solução proposta amplia o valor do trabalho operacional, tornando-o mais estratégico e reconhecido dentro da organização.


#### Conclusão Geral das Personas  

A análise das jornadas das diferentes personas permitiu compreender de forma ampla os desafios enfrentados por gestores e equipes financeiras no dia a dia. Apesar de apresentarem necessidades específicas, todas convergem para pontos comuns: a busca por informações confiáveis, previsões assertivas e maior autonomia na tomada de decisões.  


#### 4.1.8 Política de Privacidade

Modelo de Política de Privacidade: 
Projeto Fint-li

##### Informações Gerais 
&emsp; Esta política de privacidade informa como o projeto [ Fint-li ], desenvolvido pelo grupo [ Ceraza ], trata os dados pessoais coletados durante o uso da solução. \
Está em conformidade com a Lei nº 13.709/2018 – LGPD.

##### Dados Coletados 
&emsp; Dados fornecidos diretamente: valor da cobrança, valor pago, data de vencimento, data de pagamento, histórico de pagamentos e inadimplência. <br>
&emsp; Dados coletados automaticamente:  logs de transações, metadados anonimizados para análise preditiva (sem identificação direta do titular).

##### Finalidade do Tratamento 

&emsp; Os dados são utilizados exclusivamente para fins educacionais, com o objetivo de construir uma Prova de Conceito (POC) de um modelo preditivo de inadimplência. 

O tratamento se destina a fins estatísticos, analíticos e acadêmicos, sem finalidade comercial.

##### Armazenamento e Retenção
&emsp; Local: Servidores institucionais ou ambientes protegidos em nuvem (com autenticação controlada pelo grupo). <br>
&emsp; Prazo: Os dados serão armazenados apenas durante o ciclo de desenvolvimento do projeto (estimado em até 10 semanas ), sendo excluídos de forma segura ao final da atividade.

##### Compartilhamento de Dados 
&emsp;  Os dados não serão compartilhados com terceiros. A única exceção é o acesso restrito aos orientadores acadêmicos do Inteli para fins de orientação e avaliação, conforme previsto na parceria com a empresa Finnet.

##### Segurança dos Dados 
&emsp; Medidas técnicas e organizacionais foram adotadas para garantir a integridade, confidencialidade e disponibilidade dos dados, incluindo:

- Exclusão segura após o encerramento do projeto;
- Monitoramento de movimentações internas dos dados;
- Criptografia em repouso (quando aplicável);
- Controle de acesso por autenticação individual.

##### Direitos dos Titulares 
&emsp; Em conformidade com a LGPD, os titulares dos dados possuem os seguintes direitos:

- Confirmação da existência de tratamento;
- Acesso aos dados;
- Correção de dados incompletos ou inexatos;
- Anonimização, bloqueio ou eliminação de dados desnecessários;
- Revogação do consentimento (quando aplicável);
- Peticionamento à ANPD, conforme art. 18 da LGPD.

&emsp; Embora os dados sejam fornecidos diretamente pela empresa parceira e estejam anonimizados, o projeto se compromete com o respeito aos direitos dos titulares em todas as fases do tratamento.

&emsp; Acesso, correção, exclusão, revogação de consentimento.

&emsp; Solicitações via e-mail: [e-mail do responsável]

##### Encarregado de Dados (DPO) 
Nome: [nome do responsável]
E-mail: [e-mail de contato]

### 4.2. Compreensão dos Dados

#### 4.2.1. Exploração de dados

- A exploração dos dados foi realizada a partir da base fornecida pela Finnet (planilhas Excel), com carregamento no `notbook.ipynb` e uso de `pandas`, `seaborn` e `matplotlib`.
- Para colunas numéricas, foram calculadas estatísticas descritivas: contagem, média, desvio-padrão, mínimo, quartis (25%, 50%/mediana, 75%) e máximo, além de mediana e moda adicionais. Para colunas categóricas, avaliou-se a frequência de categorias e a presença de valores ausentes.
<div align="center">
  <p><strong>Tabela da análise descritiva basica<strong></p>
  <img src="../assets/analise_descritiva.png" alt="Histograma de vl_boleto" width="85%" >
</div>

### **Tipagem das colunas**

| Coluna                      | Tipo       | Observação                              |
| --------------------------- | ---------- | --------------------------------------- |
| id_grupo                    | Numérica   | Identificador                           |
| id_beneficiario             | Numérica   | Identificador                           |
| Numero_do_boleto            | Numérica   | Identificador                           |
| data_inclusao               | Temporal   | Data/hora                               |
| status_boleto               | Categórica |                                         |
| data_vencto                 | Temporal   | Data de vencimento                      |
| vl_boleto                   | Numérica   | Valor monetário                         |
| dt_pagto                    | Temporal   | Data de pagamento                       |
| vl_pagto                    | Numérica   | Valor monetário                         |
| banco                       | Categórica |                                         |
| id_pagador                  | Numérica   | Identificador                           |
| pagador_cep                 | Categórica | CEP tratado como texto                  |
| pagador_cidade              | Categórica |                                         |
| qtd_acessos_pagador         | Numérica   | Contagem                                |
| pagador_dt_ultimo_acesso    | Temporal   | Data/hora                               |
| pagador_cnpjcpf             | Categórica | Tipo do documento (CPF/CNPJ)            |

### Gráficos gerados

Durante a etapa de exploração de dados, diferentes gráficos foram criados com o objetivo de investigar padrões relevantes para o modelo preditivo de inadimplência. Os principais tipos de análise e seus respectivos insights estão descritos abaixo.

---

#### Gráfico 1 – Preço × Status do Boleto

<div align="center">
  <sub>Figura X - Preço X Status</sub>  
  <img src="../assets/graficos_hipoteses/grafico-1.png" alt="Preço X Status" width="90%">
</div>

Gráfico de dispersão com o valor do boleto (`vl_boleto`) no eixo X e o `status_boleto` no eixo Y (LIQUIDADO, BAIXADO, REGISTRADO, PROTESTADO).

Apesar de grande concentração de boletos de valor mais baixo em todos os status, existem boletos de alto valor em todos os grupos, inclusive liquidados. Isso sugere **baixa correlação direta entre valor do boleto e status de inadimplência**, descartando a hipótese de que apenas valores altos estejam associados à falta de pagamento.

---

#### Gráfico 2 – Inadimplência × Período de Pagamento

<div align="center">
  <sub>Figura X - Inadimplência X Período</sub>  
  <img src="../assets/graficos_hipoteses/grafico-2.png" alt="Inadimplência X Período" width="90%">
</div>
 
Gráfico de barras agrupadas mostrando a quantidade de boletos adimplentes (azul) e inadimplentes (vermelho), agrupados por faixas do número de dias entre a data de inclusão e a data de vencimento.

Conforme o período de pagamento aumenta, a **proporção de inadimplentes também cresce**, indicando uma tendência entre prazos mais longos e maior risco. Essa variável é forte candidata a ser usada como feature no modelo.

---

#### Gráfico 3 – Acessos ao boleto × Status do Boleto

<div align="center">
  <sub>Figura X - Acessos X Status</sub>  
  <img src="../assets/graficos_hipoteses/grafico-3.png" alt="Acessos X Status" width="90%">
</div>

Gráfico de dispersão com a quantidade de acessos ao sistema por parte do pagador (`qtd_acessos_pagador`) no eixo X, e o `status_boleto` no eixo Y.
  
Boletos liquidados concentram-se em faixas de maior acesso, enquanto boletos protestados ou baixados tendem a estar associados a poucos acessos. Isso reforça a hipótese de que **acessos frequentes estão ligados à adimplência** e pode indicar engajamento ou controle financeiro do pagador.

---

#### Gráfico 4 – Taxa de Inadimplência (%) por Banco

<div align="center">
  <sub>Figura X - Inadimplência por Banco</sub>  
  <img src="../assets/graficos_hipoteses/grafico-4.png" alt="Taxa por Banco" width="90%">
</div>

Bubble chart que relaciona bancos com sua respectiva taxa de inadimplência. O tamanho da bolha representa o volume de boletos e a cor representa o nível da taxa (verde = baixa, vermelho = alta).

Bancos como **Citibank** e **Banco do Brasil** apresentam taxas de inadimplência bem acima da média. Essa visualização orienta a criação de **variáveis categóricas com risco atribuído por instituição bancária**, o que pode melhorar a capacidade preditiva do modelo.

---

#### Tabelas estatísticas e dados categóricos

As estatísticas descritivas básicas (média, mediana, desvio padrão, quartis e outliers) foram calculadas para todas as variáveis numéricas (`vl_boleto`, `vl_pagto`, `qtd_acessos_pagador`, entre outras).

Essas tabelas permitiram:

- Detectar distribuições assimétricas;
- Identificar a necessidade de **escalonamento robusto** (RobustScaler) para valores monetários;
- Confirmar a presença de **outliers visuais**;
- Apoiar a definição das hipóteses de modelagem.

As **frequências das variáveis categóricas** (como `status_boleto`, `banco`, `pagador_cidade`) também foram avaliadas e utilizadas para definir variáveis dummy ou agrupamentos personalizados.

As tabelas completas podem ser encontradas no arquivo `notbook.ipynb`.

---

#### Conclusão da análise exploratória

A análise visual e estatística permitiu identificar padrões relevantes entre comportamento de pagamento, prazos, engajamento e instituições bancárias. Esses padrões fundamentam a formulação de hipóteses e orientam a seleção das variáveis preditivas, garantindo um início consistente para as etapas de modelagem supervisionada.

---
#### 4.2.2. Pré-processamento dos dado


O pré-processamento dos dados foi realizado de forma sistemática para garantir a qualidade e a consistência do conjunto de dados antes da modelagem. Este processo, documentado no notebook `notbook.ipynb`, envolveu etapas específicas de limpeza, padronização e transformação.

---

#### ******* Tratamento de Valores Ausentes

A primeira etapa consistiu na identificação e tratamento de valores ausentes, que apresentavam duas representações: strings **"\N"** e a representação padrão do Pandas, **`NaN`**.

Para padronizar, todos os valores **"\N"** foram substituídos por **`NaN`**. Em seguida, uma estratégia específica foi aplicada por tipo de variável:

* **Variáveis numéricas monetárias** (`vl_pagto`, `vl_boleto`): preenchimento com `0`.
* **Variáveis de contagem** (`qtd_acessos_pagador`): preenchimento com `0` e conversão para o tipo inteiro.
* **Variáveis categóricas** (`pagador_cidade`): preenchimento com "Não informado".
* **Variáveis de identificação** (`pagador_cep`): conversão para numérico.

#### *******. Correção dos Tipos de Dados

Após o tratamento dos valores ausentes, os tipos de dados foram corrigidos para garantir a consistência, um passo fundamental para a análise e o uso nos modelos.

* **`vl_pagto` e `vl_boleto`**: convertidos para `float`.
* **`qtd_acessos_pagador`**: convertido para `int`.
* **`pagador_cep`**: convertido para numérico com o tratamento de erros (`errors='coerce'`).

#### *******. Identificação de Outliers

A identificação de outliers foi feita por meio de uma análise visual utilizando **boxplots** para as principais variáveis numéricas: `vl_boleto`, `vl_pagto` e `qtd_acessos_pagador`.

O objetivo foi documentar os pontos fora do intervalo interquartil (IQR) e analisar a assimetria das distribuições. **Nesta etapa, não foi aplicada correção automática de outliers**. O resultado servirá de guia para futuras decisões de tratamento, como a winsorização ou a análise por segmentos.

#### *******. Normalização de Variáveis Numéricas

Para preparar os dados para a modelagem e facilitar as comparações, aplicou-se a **normalização Min-Max** nas variáveis `vl_boleto`, `vl_pagto` e `qtd_acessos_pagador`.

A normalização escala os dados para o intervalo entre [0, 1] usando a fórmula:
```
X_normalized = (X - X_min) / (X_max - X_min)
```
Essa técnica garante que algoritmos sensíveis à escala funcionem corretamente, mantendo a distribuição original dos dados.

#### *******. Validação e Benefícios

Ao final do processo, foi realizada uma validação completa para confirmar a ausência de valores ausentes e a correção dos tipos de dados.

O pré-processamento trouxe os seguintes benefícios:
* **Consistência e Qualidade:** os dados foram padronizados, removendo inconsistências que poderiam prejudicar a análise.
* **Prontidão para Modelagem:** as variáveis numéricas foram preparadas para os algoritmos de machine learning.
* **Reprodutibilidade:** o processo foi totalmente documentado, permitindo que qualquer pessoa o repita com os mesmos resultados.

#### 4.2.3. Hipóteses

#### **Hipótese** 1 — Preço × Status

**Pergunta:** existe relação entre o **valor do boleto (R$)** e o **status** (LIQUIDADO/BAIXADO/REGISTRADO/PROTESTADO)?

**Descrição do gráfico:** diagrama de dispersão com vl\_boleto no eixo X e status\_boleto no eixo Y; cada ponto representa um boleto. Observam-se muitos boletos de baixo valor próximos de zero e alguns outliers (na ordem de milhões) espalhados entre diferentes status.

<div align="center">
  
  <sub>Gráfico da hipótese 1</sub>  

  <img src="../assets/graficos_hipoteses/grafico-1.png" alt="Gráfico da hipótese 1" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

**Conclusão:** _O gráfico mostra que os valores dos boletos não necessariamente estão ligados ao boleto ser liquidado._

**Evidências que sustentam a conclusão:**

*   Há pontos **LIQUIDADOS** tanto em valores baixos quanto muito altos (há boletos > R$10M liquidados), indicando ausência de um limiar claro de valor que “garanta” liquidação.
    
*   Também há **BAIXADOS/REGISTRADOS/PROTESTADOS** distribuídos ao longo de diversas faixas de valor, sem padrão monotônico visível.
    
*   A dispersão é semelhante entre status nas faixas mais densas (baixa faixa de valor), o que sugere **baixa correlação** entre valor e status.

---

#### **Hipótese** 2 — Inadimplência × Período de pagamento

**Pergunta:** prazos maiores entre **inclusão** e **vencimento** aumentam a **inadimplência**?

**Descrição do gráfico:** histograma com barras **lado a lado** (barmode = "group") por faixas de periodo\_de\_pagamento\_dias. Em cada faixa, a barra **azul** representa **Adimplentes** e a **vermelha** representa **Inadimplentes**.

<div align="center">
  
  <sub>Gráfico da hipótese 2</sub>  

  <img src="../assets/graficos_hipoteses/grafico-2.png" alt="Gráfico da hipótese 2" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

**Conclusão:** Com o aumento dos períodos podemos ver um aumento na inadimplência em comparação aos não inadimplentes.

**Evidências que sustentam a conclusão:**

*   À medida que avançamos para **faixas de período maiores**, cresce a **proporção** de barras vermelhas (inadimplentes) em relação às azuis (adimplentes).
    
*   O comportamento é consistente ao longo de múltiplas janelas de dias, sugerindo **tendência positiva** entre prazo e probabilidade de inadimplência.

---

#### **Hipótese** 3 — Taxa de Inadimplência (%) por Banco

**Pergunta:** a inadimplência varia de forma relevante entre bancos?

**Descrição do gráfico:** _bubble chart_ com cada banco no eixo X e a **taxa de inadimplência (%)** no eixo Y.

O **tamanho** da bolha representa o **volume total de boletos** do banco e a **cor** (escala RdYlGn\_r) reforça a intensidade da taxa.

<div align="center">
  
  <sub>Gráfico da hipótese 2</sub>  

  <img src="../assets/graficos_hipoteses/grafico-3.png" alt="Gráfico da hipótese 3" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>


**Conclusão:** O grupo de pagadores que utilizam dos serviços do Banco Citi e do Banco do Brasil, tem uma tendência de ser inadimplente.

**Evidências que sustentam a conclusão:**

*   O ponto do **BANCO CITIBANK S.A.** aparece no topo da escala (≈ muito alto), indicando **taxa de inadimplência substancialmente maior** que a média dos demais.
    
*   O **BANCO DO BRASIL** também se destaca com **taxa elevada**, acima de vários pares do painel.
    
*   A visualização controla parcialmente o viés de volume: bolhas maiores (mais boletos) nem sempre têm maior taxa, reforçando que há **efeito por banco**, não apenas por quantidade.

---

### 4.3. Preparação dos Dados e Modelagem

```
Caso seu projeto seja Modelo Supervisionado, apresentar:
a) Organização dos dados (conjunto de treinamento, validação e testes)
b) Modelagem para o problema (proposta de features com a explicação completa da linha de raciocínio).
c) Métricas relacionadas ao modelo (pelo menos 3).
d) Apresentar o primeiro modelo candidato, e uma discussão sobre os resultados deste modelo (discussão sobre as métricas para esse modelo candidato).

Caso seu projeto seja Modelo Não-Supervisionado, apresentar:
a) Modelagem para o problema (proposta de features com a explicação completa da linha de raciocínio).
b) Primeiro modelo candidato para o problema.
c) Justificativa para a definição do K do modelo.
d) Escolha de um tipo de sistema de recomendação e a justificativa para essa escolha.

Remova este bloco ao final
```

### 4.4. Comparação de Modelos

```
- Descrever e justificar a escolha da métrica de avaliação dos modelos com base no que é mais importante para o problema ao
  se medir a qualidade desses modelos;
- Descrever ao menos três modelos candidatos, seus respectivos algoritmos, seus tunings de hiperparâmetros e suas métricas
  alcançadas;

Remova este bloco ao final
```

### 4.5. Avaliação

```
- Descreva a solução final de modelo preditivo e justifique a escolha. Alinhe sua justificativa com a Seção 4.1, resgatando o entendimento
  do negócio e das personas, explicando de que formas seu modelo atende os requisitos e definições.
- Descreva também um plano de contingência para os casos em que o modelo falhar em suas predições.
- Além disso, discuta sobre a explicabilidade do modelo (se aplicável) e realize a verificação de aceitação ou refutação das hipóteses.
- Se aplicável, utilize equações, tabelas e gráficos de visualização de dados para melhor ilustrar seus argumentos.

Remova este bloco ao final
```

## <a name="c5"></a>5. Conclusões e Recomendações

```
Escreva, de forma resumida, sobre os principais resultados do seu projeto e faça recomendações formais ao seu parceiro de negócios em relação ao uso desse modelo. Você pode aproveitar este espaço para comentar sobre possíveis materiais extras, como um manual de usuário mais detalhado na seção “Anexos”. Não se esqueça também das pessoas que serão potencialmente afetadas pelas decisões do modelo preditivo e elabore recomendações que ajudem seu parceiro a tratá-las de maneira estratégica e ética.

Remova este bloco ao final
```

## <a name="c6"></a>6. Referências

### Seção 4.1.1

NEXXERA TECNOLOGIA E SERVIÇOS S.A. *Melhores resultados e gestão facilitada para o seu ecossistema de negócios*. Disponível em: [https://xx.nexxera.com/nexxera-hubly-geral/?utm_source=google_ads&utm_campaign=rz_in[…]sKS5P39XGtvdtwiDnDHePrVS1ZfgihP8aO5Bz186u4GNVKaQaAiv2EALw_wcB](https://xx.nexxera.com/nexxera-hubly-geral/?utm_source=google_ads&utm_campaign=rz_in[…]sKS5P39XGtvdtwiDnDHePrVS1ZfgihP8aO5Bz186u4GNVKaQaAiv2EALw_wcB). Acesso em: 05 ago. 2025. 

TI INSIDE. *Finnet lança plataforma de cobranças e recebimentos que permite utilizar taxas já negociadas com os bancos*. TI INSIDE Online, 06 out. 2022. Disponível em: https://tiinside.com.br/06/10/2022/finnet-lanca-plataforma-de-cobrancas-e-recebimentos-que-permite-utilizar-taxas-ja-negociadas-com-os-bancos/#:~:text=. Acesso em: 05 ago. 2025.

FINSIDERS BRASIL. *Techfin da Totvs com Itaú amplia portfólio e avança no ERP banking*. Finsiders Brasil, 16 mai. 2024. Disponível em: https://finsidersbrasil.com.br/reportagem-exclusiva-fintechs/techfin-da-totvs-com-itau-amplia-portfolio-e-avanca-no-erp-banking/#:~:text=. Acesso em: 05 ago. 2025.

---

### Seção 4.1.8 
Referências (Norma ABNT NBR 6023)
BRASIL. Lei nº 13.709, de 14 de agosto de 2018. Dispõe sobre a proteção de dados pessoais e altera a Lei nº 12.965, de 23 de abril de 2014 (Lei Geral de Proteção de Dados Pessoais - LGPD). Diário Oficial da União: seção 1, Brasília, DF, ano 155, n. 157-A, p. 1-10, 15 ago. 2018.

AUTORIDADE NACIONAL DE PROTEÇÃO DE DADOS. Cartilha educativa – LGPD para iniciantes. Brasília: ANPD, 2023. Disponível em: https://www.gov.br/anpd/pt-br/documentos-e-publicacoes/cartilha-lgpd-para-iniciantes.pdf. Acesso em: 07 ago. 2025.

JUSBRASIL. Devo me preocupar com a LGPD? São Paulo: JusBrasil, 2021. Disponível em: https://jus.com.br/artigos/91636/devo-me-preocupar-com-a-lgpd. Acesso em: 07 ago. 2025.

MIT TECHNOLOGY REVIEW BRASIL. LGPD: conheça seus direitos como titular de dados pessoais. São Paulo: MIT Technology Review, 2023. Disponível em: https://mittechreview.com.br/lgpd-conheca-seus-direitos-como-titular-de-dados-pessoais/. Acesso em: 07 ago. 2025.

CEGAS E DEMOCRACIA. LGPD na prática. [S. l.], 2023. Vídeo (9min40s). Disponível em: https://www.youtube.com/watch?v=hu6XIc7QVnE. Acesso em: 07 ago. 2025.


## <a name="attachments"></a>Anexos

## 7. Distribuição Normal e Teste de Hipótese

&emsp; Nesta seção, é apresentada a análise detalhada das variáveis quantitativas do conjunto de dados, com foco em dois aspectos fundamentais: a verificação da normalidade e o escalonamento das variáveis.

&emsp; A análise apresentada nesta seção foi estruturada de forma sistemática: inicialmente, a normalidade das variáveis foi examinada, em seguida, o escalonamento foi aplicado, e por fim, os resultados antes e depois da transformação foram comparados visual e numericamente, garantindo que o comportamento das variáveis fosse interpretado de forma completa e consistente.

### Análise e teste de normalidade das variáveis quantitativas

&emsp; Nesta primeira parte, é apresentada a análise de normalidade de três variáveis quantitativas do conjunto de dados, com o objetivo de verificar se elas seguem uma distribuição normal.

&emsp; As variáveis analisadas foram:

- *valor boleto*: representa o valor do boleto gerado para cada transação;
- *valor_pago*: corresponde ao valor efetivamente pago pelo pagador;
- *qtde_acessado_pagador*: indica a quantidade de acessos realizados pelo pagador ao sistema.

&emsp; Cada variável foi tratada e limpa antes da análise, removendo-se valores não numéricos ou ausentes, garantindo que os testes de normalidade fossem aplicados corretamente. O comportamento dessas variáveis foi avaliado por meio de testes estatísticos formais, histogramas e comparação entre medidas de tendência central, como média e mediana, permitindo uma interpretação detalhada da distribuição dos dados.

---
#### Teste para averificar se as variáveis quantitativas seguem a distribuição normal

&emsp; Uma afirmação é formulada para verificar se os dados das variáveis quantitativas seguem uma distribuição normal. Para isso, foram definidas as hipóteses estatísticas correspondentes ao teste de normalidade.

Afirmação de teste: “A variável segue uma distribuição normal”
  - **H0**: As variáveis *valor boleto*, *valor_pago* e *qtde_acessado_pagador* segue distribuição normal;
  - **H1**: A variável não segue distribuição normal.

&emsp; A hipótese nula será testada utilizando um teste estatístico adequado, permitindo que se avalie se as variáveis analisadas apresentam conformidade com a distribuição normal ou se existe evidência de assimetria ou outliers significativos.

---

#### Nível de significância adotado

&emsp; Foi definido um nível de significância para o teste de normalidade, com o objetivo de estabelecer o critério estatístico que será utilizado para aceitar ou rejeitar a hipótese nula. O nível de significância determina a probabilidade de erro tipo I, ou seja, a chance de rejeitar a hipótese nula quando ela é verdadeira.

&emsp; Define-se **α = 0,05 (5%)**
- Se o valor de **p** for menor que **α**, rejeitamos **H0** => a variável não segue distribuição normal;
- Se o valor de **p** for maior ou igual a **α**, não rejeitamos **H0** => a variável pode ser considerada normal.

&emsp; A comparação entre o p-valor e o nível de significância permite que a normalidade das variáveis seja avaliada de forma objetiva, fornecendo suporte estatístico para decisões subsequentes de análise e modelagem de dados.

---

#### Teste de normalidade em 3 variáveis quantitativas:

&emsp; Foi realizado um teste de normalidade em três variáveis quantitativas do conjunto de dados para verificar se os valores seguem uma distribuição normal. O teste estatístico escolhido foi o Shapiro-Wilk, que é considerado adequado para conjuntos de dados de pequeno e médio porte e é amplamente utilizado para avaliação de normalidade.

&emsp; Variáveis analisadas

- *valor boleto*: valor do boleto gerado;
- *valor_pago*: valor efetivamente pago;
- *qtde_acessado_pagador*: quantidade de acessos realizados pelo pagador ao sistema.

```python
import pandas as pd # importa a biblioteca pandas
from scipy.stats import shapiro # importa o teste de normalidade utilizado

# lista as variáveis utilizadas
variaveis = pd.Series({
    "valor_boleto": "valor boleto",
    "valor_pago": "valor_pago",
    "qtde_acessado_pagador": "qtde_acessado_pagador"
})

# função de teste
def testar_normalidade(dados, alpha=0.05):
    # teste de Shapiro-Wilk
    stat, p = shapiro(dados)
    resultado = "Segue distribuição normal" if p > alpha else "Não segue distribuição normal"
    return (p), resultado


resultados = []

for nome_variavel, coluna_df in variaveis.items():
    # limpeza dos dados, convertendo-os para numéricos e eliminando os valores nulos
    dados = pd.to_numeric(df[coluna_df], errors="coerce").dropna()
    
    # executa o teste
    p_valor, resultado = testar_normalidade(dados)
    
    # armazena o resultado
    resultados.append({
        "Variável": nome_variavel,
        "p-valor": p_valor,
        "Resultado": resultado
    })

# resultado do teste
tabela_resultados = pd.DataFrame(resultados)

print("=== Teste de Normalidade Shapiro-Wilk ===")
print(tabela_resultados)
```

<div align="center">
  
  <sub>Figura 07 - Resultado do teste</sub>  

  <img src="../assets\histogramas_e_testes_de_normalidade\teste_shapiro_wilk.png" alt="Imagem do teste de normalidae" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>
</div>

&emsp; Os resultados do teste de Shapiro-Wilk indicam que as três variáveis analisadas apresentam p-valores inferiores ao nível de significância de 0,05, o que leva à rejeição da hipótese nula de normalidade. Isso sugere que essas variáveis não seguem uma distribuição normal, o que tem implicações importantes para a escolha de métodos estatísticos subsequentes — como o uso de testes não paramétricos ou transformações de dados.

---

**Histogramas das variáveis escolhidas:** 

&emsp; Foram gerados histogramas para as três variáveis quantitativas analisadas, com o objetivo de fornecer uma representação visual da distribuição dos dados e verificar se os resultados obtidos no teste de normalidade são coerentes com o comportamento observado graficamente.

```python
import pandas as pd # importa a biblioteca pandas
import matplotlib.pyplot as plt # importa a biblioteca para plotar os gráficos


# lista as variáveis utilizadas
variaveis = pd.Series({
    "valor_boleto": "valor boleto",
    "valor_pago": "valor_pago",
    "qtde_acessado_pagador": "qtde_acessado_pagador"
})

# loop para plotar um histograma para cada variável
for nome_variavel, coluna_df in variaveis.items():
    # limpeza dos dados, convertendo-os para numéricos e eliminando os valores nulos
    dados = pd.to_numeric(df[coluna_df], errors="coerce").dropna()
    
    # plota os histogramas
    plt.figure(figsize=(6, 4))
    plt.hist(dados, bins=50, color="skyblue", edgecolor="black")
    plt.title(f"Histograma de {nome_variavel}")
    plt.xlabel(nome_variavel)
    plt.ylabel("Frequência")
    plt.grid(axis="y", linestyle="--", alpha=0.7)
    plt.show()
```

##### Análise de histograma da variável "valor boleto":

<div align="center">
  
  <sub>Figura 08 - Histograma variável valor boleto</sub>  

  <img src="../assets/histogramas_e_testes_de_normalidade/histograma_valor_boleto.png" alt="Imagem do histograma da variável valor boleto" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

&emsp; O histograma de *valor boleto* apresenta uma distribuição assimétrica à direita, com uma concentração de valores baixos e uma cauda longa de valores altos. Essa forma distorcida reforça o resultado do teste de Shapiro-Wilk, indicando ausência de normalidade.

&emsp; A ausência de simetria e o formato não gaussiano dos histogramas corroboram a conclusão de que as variáveis não seguem distribuição normal.

##### Análise de histograma da variável "valor_pago":

<div align="center">
  
  <sub>Figura 09 - Histograma variável valor_pago</sub>  

  <img src="../assets/histogramas_e_testes_de_normalidade/histograma_valor_pago.png" alt="Imagem do histograma da variável valor_pago" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

<br>

&emsp; O histograma de *valor_pago* também mostra forte assimetria, com muitos valores próximos de zero e alguns valores muito altos. A presença de outliers visuais e a falta de simetria corroboram a conclusão de que a variável não segue uma distribuição normal.

##### Análise de histograma da variável "qtde_acessado_pagador":

<div align="center">
  
  <sub>Figura 10 - Histograma variável qtde_acessado_pagador</sub>  

  <img src="../assets/histogramas_e_testes_de_normalidade/histograma_qtde_acessado_pagador.png" alt="Imagem do histograma da variável qtde_acessado_pagador" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

&emsp; O histograma dessa variável revela uma distribuição discreta e altamente concentrada em poucos valores, com picos acentuados. A ausência de uma curva suave e simétrica é mais uma evidência visual de que a variável não é normalmente distribuída.

&emsp; A análise visual dos histogramas foi utilizada para reforçar os resultados do teste de normalidade. Observou-se que as variáveis com evidência estatística de não normalidade apresentaram claramente assimetria e outliers. Dessa forma, os histogramas confirmam e complementam os resultados obtidos pelo teste Shapiro-Wilk.

---

##### Comparação entre média e mediana

&emsp; Foram calculadas a média e a mediana das três variáveis quantitativas analisadas, com o objetivo de fornecer uma avaliação adicional da distribuição dos dados e verificar se os resultados reforçam os obtidos no teste de normalidade.

```python
import pandas as pd # importa a biblioteca pandas

# lista as variáveis utilizadas
variaveis = pd.Series({
    "valor_boleto": "valor boleto",
    "valor_pago": "valor_pago",
    "qtde_acessado_pagador": "qtde_acessado_pagador"
})

# lista para armazenar resultados
comparacao = []

# loop para calcular média e mediana de cada variável
for nome_variavel, coluna_df in variaveis.items():
    # limpeza dos dados, convertendo-os para numéricos e eliminando os valores nulos
    dados = pd.to_numeric(df[coluna_df], errors="coerce").dropna()
    
    # calcula a média e a mediana das variáveis
    media = dados.mean()
    mediana = dados.median()

    # adiciona os resultados a um dicionário
    comparacao.append({
        "Variável": nome_variavel,
        "Média": round(media, 2),
        "Mediana": round(mediana, 2)
    })

# cria tabela
tabela_comparacao = pd.DataFrame(comparacao)

print("=== Comparação entre Média e Mediana ===")
print(tabela_comparacao)
```

<div align="center">
  
  <sub>Figura 11 - Tabela de comparação</sub>  

  <img src="../assets/histogramas_e_testes_de_normalidade/comparacao_media_mediana.png" alt="Imagem da tabela de comparação" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

&emsp; A diferença significativa entre média e mediana indica assimetria nas distribuições. Isso reforça a conclusão dos testes de normalidade, pois em distribuições normais, média e mediana tendem a ser semelhantes.

&emsp; Com base nos testes estatísticos, nas análises gráficas e na comparação entre medidas de tendência central, conclui-se que as variáveis analisadas não apresentam evidência de normalidade. Essa constatação é fundamental para orientar a escolha de métodos estatísticos apropriados nas etapas seguintes da análise.

---

&emsp; Foi avaliada a normalidade das variáveis quantitativas. Todas as três variáveis analisadas (valor boleto, valor_pago e qtde_acessado_pagador) apresentaram evidência de não normalidade, o que foi confirmado pelos histogramas e pela comparação entre média e mediana. Esses resultados indicam que devem ser consideradas transformações ou escalonamentos adequados nas etapas subsequentes de análise.

---

### Escalonamento (Padronização ou normalização) nas variáveis quantitativas

&emsp; Nesta segunda parte, foi realizado o escalonamento das três variáveis quantitativas previamente analisadas, com o objetivo de ajustar suas escalas e reduzir o impacto de outliers. Para isso, técnicas de padronização (RobustScaler) e normalização (MinMaxScaler) foram aplicadas de acordo com as características de cada variável, garantindo que os dados pudessem ser comparados de forma adequada e preparados para análises subsequentes.

---

#### Tipo de escalonamento

&emsp; Analisando as variáveis escolhidas, escolhe-se um tipo de escalonamento adequado para cada uma, considerando a distribuição e os valores disponíveis.

&emsp; As variáveis *valor boleto* e *valor_pago* possuem certas similaridades, o que justifica o uso do escalonamento escolhido. Ambas apresentam valores monetários com alta variação e presença de outliers, por isso utiliza-se o método Robust Scaler. Esse mecanismo utiliza a mediana e o intervalo interquartil, a fim de reduzir a influência de valores extremos e manter a representatividade da maior parte dos dados, evitando distorções causadas por casos atípicos.

&emsp; Em contrapartida, a variável *qtde_acessado_pagador*, que representa uma contagem de acessos aos boletos disponíveis, possui escala limitada e comportamento mais discreto. Nesse contexto, o Min-Max Scaler é o mais apropriado, pois normaliza os valores para o intervalo [0, 1], facilitando a comparação entre registros e preservando as proporções entre as quantidades observadas.

&emsp; Portanto, conclui-se que as variáveis devem ser tratadas com diferentes técnicas de escalonamento de acordo com suas peculiaridades: *valor boleto* e *valor_pago* com Robust Scaler e *qtde_acessado_pagador* com Min-Max Scaler, garantindo um pré-processamento consistente e adequado para os modelos preditivos.

---

#### Dados estatísticos utilizados

&emsp; Para aplicar as técnicas de escalonamento escolhidas, é necessário levantar os principais dados estatísticos das variáveis selecionadas. Esses valores servirão de referência para os cálculos de transformação, permitindo que cada variável seja ajustada conforme sua distribuição e escala.

**Estatísticas para Robust Scaler**

| Variável                | Mediana (Q2) | Q1 (25%) | Q3 (75%) | IQR (Q3 - Q1) |
| ----------------------- | ------------ | -------- | -------- | ------------- |
| valor\_boleto           |2900,115|1122,005|7274,8525|6152,8475|
| valor\_pago             |2729,620|1081,230|6949,9850|5868,755|

<br>

**Estatísticas para Min-Max Scaler**

| Variável                | Valor Mínimo (x\_min) | Valor Máximo (x\_max) |
| ----------------------- | --------------------- | --------------------- |
| qtde\_acessado\_pagador |1|69|


&emsp; Portanto, para cada variável escolhida serão calculados e reportados dados específicos que serão utilizados. Esses parâmetros garantirão que os métodos de escalonamento (Robust Scaler e Min-Max Scaler) sejam aplicados de forma consistente e alinhada às características estatísticas dos dados.

---

#### Equação das variáveis

&emsp; A seguir, estão as equações específicas de cada variável conforme o método de escalonamento escolhido.


**Equação para Robust Scaler**

$$ x' = \frac{x - Q2}{Q3 - Q1} $$

- x = valor original
- x' = valor escalonado  
- Q2 = Mediana da variável  
- Q1 = Primeiro quartil (25%)  
- Q3 = Terceiro quartil (75%)  

Assim, para a variável *valor\_boleto* temos:

$$ x' = \frac{x - 2900,115}{7274,8525 - 1122,005} $$
$$ x' = \frac{x - 2900,115}{6152,8475} $$

Já para a variável *valor\_pago* temos:

$$ x' = \frac{x - 2729,620}{6949,9850 - 1081,230} $$
$$ x' = \frac{x - 2729,620}{5868,755} $$

**Equação para Min-Max Scaler**

$$ x' = \frac{x - x_{min}}{x_{max} - x_{min}} $$

- x = valor original  
- x' = valor escalonado  
- x_{min} = valor mínimo da variável  
- x_{max} = valor máximo da variável  

Assim, para a variável *qtde\_acessado\_pagador* temos:

$$ x' = \frac{x - 1}{69 - 1} $$
$$ x' = \frac{x - 1}{68} $$

&emsp; Em cada fórmula específica, temos uma variável original (x) e sua representação de forma escalonada (x'). Assim, possibilitando manipular os valores analisados.

---

#### Novos histogramas das variáveis escolhidas

&emsp; A seguir, são apresentados os histogramas das três variáveis utilizadas, comparando sua distribuição antes e depois do processo de escalonamento.

**1. *valor_boleto* (Robust Scaler)**
- **Antes:** Os valores estavam concentrados em uma faixa específica, mas com alguns outliers que puxavam a distribuição para a direita.  
- **Depois:** Com o Robust Scaler, os dados foram centralizados em torno da mediana e reduzidos em função do IQR (Q3 − Q1). Isso faz com que os outliers tenham menos influência e a distribuição fique mais equilibrada.

```python
import pandas as pd # importa a biblioteca pandas
import matplotlib.pyplot as plt # importa a biblioteca para plotar os gráficos
from sklearn.preprocessing import RobustScaler # importa a função para a normalização dos dados

# limpeza dos dados, convertendo-os para numéricos e eliminando os valores nulos
valor_boleto = pd.to_numeric(df["valor boleto"], errors="coerce").dropna()

# escalonamento dos dados com RobustScaler
scaler = RobustScaler()
valor_boleto_scaled = scaler.fit_transform(valor_boleto.values.reshape(-1, 1))

# criar gráficos lado a lado
plt.figure(figsize=(12, 5))

# plotagem do histograma original
plt.subplot(1, 2, 1)
plt.hist(valor_boleto, bins=50, color="skyblue", edgecolor="black")
plt.title("Histograma - valor_boleto (Original)")
plt.xlabel("valor_boleto")
plt.ylabel("Frequência")
plt.grid(axis="y", linestyle="--", alpha=0.7)

# plotagem do histograma escalonado
plt.subplot(1, 2, 2)
plt.hist(valor_boleto_scaled, bins=50, color="lightgreen", edgecolor="black")
plt.title("Histograma - valor_boleto (Escalonado - RobustScaler)")
plt.xlabel("valor_boleto_scaled")
plt.ylabel("Frequência")
plt.grid(axis="y", linestyle="--", alpha=0.7)

# mostrar os dois gráficos
plt.tight_layout()
plt.show()
```

<div align="center">
  
  <sub>Figura 12 - Histogramas de comparação valor boleto</sub>  

  <img src="../assets/histogramas_e_testes_de_normalidade/comparacao_valorboleto.png" alt="Imagem dos gráficos de comparação" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

**2. *valor_pago* (Robust Scaler)**
- **Antes:** A variável apresentava grande dispersão, com valores variando amplamente e alguns outliers que poderiam distorcer análises baseadas na média.

- **Depois:** Com o Robust Scaler, os dados foram centralizados em torno da mediana e escalonados usando o IQR (Q3 − Q1). Isso reduz o impacto dos outliers, mantendo a distribuição proporcional e facilitando a comparação com outras variáveis.

```python
import pandas as pd # importa a biblioteca pandas
import matplotlib.pyplot as plt # importa a biblioteca para plotar os gráficos
from sklearn.preprocessing import RobustScaler # importa a função para a normalização dos dados

# limpeza dos dados, convertendo-os para numéricos e eliminando os valores nulos
valor_pago = pd.to_numeric(df["valor_pago"], errors="coerce").dropna()

# escalonamento dos dados com RobustScaler
scaler = RobustScaler()
valor_pago_scaled = scaler.fit_transform(valor_pago.values.reshape(-1, 1))

# criar gráficos lado a lado
plt.figure(figsize=(12, 5))

# plotagem do histograma original
plt.subplot(1, 2, 1)
plt.hist(valor_pago, bins=50, color="skyblue", edgecolor="black")
plt.title("Histograma - valor_pago (Original)")
plt.xlabel("valor_pago")
plt.ylabel("Frequência")
plt.grid(axis="y", linestyle="--", alpha=0.7)

# plotagem do histograma escalonado
plt.subplot(1, 2, 2)
plt.hist(valor_pago_scaled, bins=50, color="lightgreen", edgecolor="black")
plt.title("Histograma - valor_pago (Escalonado - RobustScaler)")
plt.xlabel("valor_pago_scaled")
plt.ylabel("Frequência")
plt.grid(axis="y", linestyle="--", alpha=0.7)

# mostrar os dois gráficos
plt.tight_layout()
plt.show()
```

<div align="center">
  
  <sub>Figura 13 - Histogramas de comparação valor_pago_</sub>  

  <img src="../assets/histogramas_e_testes_de_normalidade/comparacao_valorpago.png" alt="Imagem dos gráficos de comparação" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

**3. *qtde_acessado_pagador* (Normalização – Min-Max)**
- **Antes:** Os dados variavam em uma escala própria, com concentrações em alguns intervalos e diferença grande entre valores baixos e altos.  

- **Depois:** Com a normalização Min-Max, todos os valores foram reescalonados para o intervalo **[0, 1]**, preservando a forma da distribuição original, mas ajustando a escala para permitir comparação direta com outras variáveis.

```python
import pandas as pd # importa a biblioteca pandas
import matplotlib.pyplot as plt # importa a biblioteca para plotar os gráficos
from sklearn.preprocessing import MinMaxScaler # importa a função para a normalização dos dados

# limpeza dos dados, convertendo-os para numéricos e eliminando os valores nulos
qtde_acessado = pd.to_numeric(df["qtde_acessado_pagador"], errors="coerce").dropna()

# normalização dos dados com MinMaxScaler
scaler = MinMaxScaler()
qtde_acessado_normalizado = scaler.fit_transform(qtde_acessado.values.reshape(-1, 1))

# criar gráficos lado a lado
plt.figure(figsize=(12, 5))

# plotagem do histograma original
plt.subplot(1, 2, 1)
plt.hist(qtde_acessado, bins=50, color="skyblue", edgecolor="black")
plt.title("Histograma - qtde_acessado_pagador (Original)")
plt.xlabel("qtde_acessado_pagador")
plt.ylabel("Frequência")
plt.grid(axis="y", linestyle="--", alpha=0.7)

# plotagem do histograma escalonado
plt.subplot(1, 2, 2)
plt.hist(qtde_acessado_normalizado, bins=50, color="lightgreen", edgecolor="black")
plt.title("Histograma - qtde_acessado_pagador (Normalizado - MinMaxScaler)")
plt.xlabel("qtde_acessado_normalizado")
plt.ylabel("Frequência")
plt.grid(axis="y", linestyle="--", alpha=0.7)

# mostrar os dois gráficos
plt.tight_layout()
plt.show()
```

<div align="center">
  
  <sub>Figura 14 - Histogramas de comparação qtde_acessado_pagador</sub>  

  <img src="../assets/histogramas_e_testes_de_normalidade/comparacao_qtdeacessadopagador.png" alt="Imagem dos gráficos de comparação" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

&emsp; Como os dados apresentam uma grande quantidade de outliers, a visualização inicial permanece pouco informativa. Para contornar esse problema, pode-se aplicar uma transformação logarítmica, que reduz o impacto dos outliers e melhora a clareza da visualização. Segur abaixo os gráficos com visualizações mais claras, o que gera um entendimento melhor sobre o comportamento das variáveis:

```python
import pandas as pd # importa a biblioteca pandas
import numpy as np # imposta a biblioteca para as operações matemáticas
import matplotlib.pyplot as plt # importa a biblioteca para plotar os gráficos

# lista com as variáveis
variaveis = pd.Series({
    "valor_boleto": "valor boleto",
    "valor_pago": "valor_pago",
    "qtde_acessado_pagador": "qtde_acessado_pagador"
})

plt.figure(figsize=(15, 12))

# loop para cada variável
for i, (nome_variavel, coluna_df) in enumerate(variaveis.items(), 1):
    # limpeza dos dados, convertendo-os para numéricos e eliminando os valores nulos
    dados = pd.to_numeric(df[coluna_df], errors="coerce").dropna()
    
    # transformação logarítmica (usando log1p para evitar log(0))
    dados_log = np.log1p(dados)
    
    # plotagem do histograma
    plt.subplot(3, 1, i)
    plt.hist(dados_log, bins=50, color="skyblue", edgecolor="black")
    plt.title(f"Histograma logarítmico - {nome_variavel}")
    plt.xlabel(f"log1p({nome_variavel})")
    plt.ylabel("Frequência")
    plt.grid(axis="y", linestyle="--", alpha=0.7)

plt.tight_layout()
plt.show()

```

<div align="center">
  
  <sub>Figura 15 - Histogramas logarítmicos</sub>  

  <img src="../assets/histogramas_e_testes_de_normalidade/histogramas_log.png" alt="Imagem dos histogramas das variáveis após transformação logarítmica" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

##### Comparação Geral
- O processo de escalonamento não altera o formato básico da distribuição, apenas ajusta a escala de cada variável.  
- Isso é importante para algoritmos de aprendizado de máquina evitando que variáveis em escalas muito diferentes dominem os cálculos.

#### Variáveis analisadas

&emsp; A seguir, apresentamos uma comparação das três variáveis quantitativas, demonstrando seus valores originais com seus valores após o escalonamento. O processo incluiu etapas de limpeza e tratamento dos dados para garantir a precisão da análise e da visualização.

```python
import pandas as pd # importa a biblioteca pandas
from sklearn.preprocessing import RobustScaler, MinMaxScaler # importa a função para a normalização dos dados

# lista com as variáveis
variaveis_quantitativas = ["valor boleto", "valor_pago", "qtde_acessado_pagador"]

# limpeza dos dados, convertendo-os para numéricos e eliminando os valores nulos
df_limpo = df[variaveis_quantitativas].copy()
for col in variaveis_quantitativas:
    df_limpo[col] = pd.to_numeric(df_limpo[col], errors="coerce")
df_limpo = df_limpo.dropna()

# escalonamento dos dados
# RobustScaler para valor_boleto e valor_pago
scaler_robust = RobustScaler()
valor_boleto_scaled = scaler_robust.fit_transform(df_limpo[["valor boleto"]])
valor_pago_scaled = scaler_robust.fit_transform(df_limpo[["valor_pago"]])

# MinMaxScaler para qtde_acessado_pagador
scaler_minmax = MinMaxScaler()
qtde_normalizada = scaler_minmax.fit_transform(df_limpo[["qtde_acessado_pagador"]])

# criação das tabelas
# valor_boleto
tabela_valor_boleto = pd.DataFrame({
    "valor_boleto_original": df_limpo["valor boleto"].head(10).values,
    "valor_boleto_scaled": valor_boleto_scaled[:10, 0]
})

# valor_pago
tabela_valor_pago = pd.DataFrame({
    "valor_pago_original": df_limpo["valor_pago"].head(10).values,
    "valor_pago_scaled": valor_pago_scaled[:10, 0]
})

# qtde_acessado_pagador
tabela_qtde_acessado = pd.DataFrame({
    "qtde_acessado_original": df_limpo["qtde_acessado_pagador"].head(10).values,
    "qtde_acessado_normalized": qtde_normalizada[:10, 0]
})

# mostrar as tabelas
print("=== Tabela valor_boleto ===")
print(tabela_valor_boleto)

print("\n=== Tabela valor_pago ===")
print(tabela_valor_pago)

print("\n=== Tabela qtde_acessado_pagador ===")
print(tabela_qtde_acessado)
```

<div align="center">
  
  <sub>Figura 16 - Tabela de comparação valor boleto</sub>  

  <img src="../assets/histogramas_e_testes_de_normalidade/tabela_valorboleto.png" alt="Imagem da tabela de comparação" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

<div align="center">
  
  <sub>Figura 17 - Tabela de comparação valor_pago</sub>  

  <img src="../assets/histogramas_e_testes_de_normalidade/tabela_valorpago.png" alt="Imagem da tabela de comparação" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

<div align="center">
  
  <sub>Figura 18 - Tabela de comparação qtde_acessado_pagador</sub>  

  <img src="../assets/histogramas_e_testes_de_normalidade/tabela_qtdeacessadopagador.png" alt="Imagem da tabela de comparação" width="100%">
  <br>
  <sup>Fonte: produzido pelo grupo (Ceraza) com o Colab</sup>

</div>

&emsp; Foi observado que os valores escalonados mantêm a mesma ordem relativa dos dados originais, porém em escala ajustada, permitindo comparações mais consistentes entre as variáveis. Dessa forma, o escalonamento garante que diferenças de magnitude e outliers não interfiram nas análises subsequentes, facilitando a interpretação e a aplicação de métodos estatísticos e algoritmos de aprendizado de máquina.

---

&emsp; Foi realizada uma análise completa das variáveis quantitativas do conjunto de dados, envolvendo teste de normalidade e escalonamento. Todas as variáveis apresentaram evidência de não normalidade, confirmada por histogramas e pela comparação entre média e mediana. Em seguida, foi realizado o escalonamento adequado, utilizando RobustScaler e MinMaxScaler, garantindo que as variáveis fossem comparáveis e preparadas para análises estatísticas e aplicação de algoritmos de aprendizado de máquina. Dessa forma, os dados ficaram organizados e consistentes para interpretações e processamento futuros.

---
