{"cells": [{"cell_type": "markdown", "id": "7467e67c", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON><PERSON>\n", "\n", "1. **Configurações iniciais**\n", "   - 1.1  Importação das Bibliotecas Necessárias\n", "   - 1.2 Carregamento e Visualização Inicial dos Dados\n", "\n", "\n", "2. **Exploração dos Dados**\n", "\t - 2.1. <PERSON><PERSON>lise Estatística Descritiva das Colunas Numéricas\n", "   - 2.2. Classificação das Colunas por Tipo de Dados\n", "   - 2.3. Criação de gráficos para análise de dados\n", "      - 2.3.1. Gráfico 1 — Preço (R$) × Status do Boleto\n", "      - 2.3.2. Gráfico 2 — Inadimplência × Período de Pagamento (dias)\n", "      - 2.3.3. Gráfico 3 — Acessos ao Boleto × Status\n", "      - 2.3.4. Gráfico 4 — Taxa de Inadimplência (%) por Banco\n", "\n", "3. **Pré-Processamento dos Dados**\n", "   - 3.1. Tratamento de Missing Values\n", "   - 3.2. Identificação de Outliers\n", "   - 3.3. Normalização das Variáveis Quantitativas\n", "   - 3.4. Codificação Correta das Variáveis Categóricas"]}, {"cell_type": "markdown", "id": "3581af82", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "35cc5982", "metadata": {}, "source": ["# 1. Configurações iniciais\n"]}, {"cell_type": "markdown", "id": "cfecc0b7", "metadata": {}, "source": ["## 1.1  Importação das Bibliotecas Necessárias\n", "\n", "Importamos as bibliotecas essenciais para análise de dados:\n", "- `pandas`: manipulação e análise de dados\n", "- `IPython.display`: exibição formatada de resultados\n", "- `glob`: busca e manipulação de caminhos de arquivos no sistema.\n", "- `numpy`: operações matemáticas e manipulação de arrays numéricos.\n", "- `plotly.express`: essencial para a criação dos gráficos.\n", "- `unicodedata`: normalização e tratamento de caracteres Unicode (útil para padronizar textos e remover acentuação)"]}, {"cell_type": "code", "execution_count": null, "id": "51831a26", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from IPython.display import display\n", "import glob\n", "import numpy as np\n", "import plotly.express as px\n", "import unicodedata\n"]}, {"cell_type": "markdown", "id": "7e4a844f", "metadata": {}, "source": ["## 1.2 Carregamento e Visualização Inicial dos Dados\n", "\n", "Carregamos a base de dados da Finnet e visualizamos as primeiras 5 linhas para entender a estrutura dos dados."]}, {"cell_type": "code", "execution_count": null, "id": "82b7e621", "metadata": {}, "outputs": [], "source": ["arquivos = glob.glob(\"../Dados_Finnet/*.xlsx\")\n", "dfs = [pd.read_excel(arq) for arq in arquivos]\n", "dt = pd.concat(dfs, ignore_index=True)\n", "\n", "display(dt.head())"]}, {"cell_type": "markdown", "id": "c8945467", "metadata": {}, "source": ["Contagem de registros no DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "a7164fca", "metadata": {}, "outputs": [], "source": ["dt.shape[0]"]}, {"cell_type": "markdown", "id": "98d18590", "metadata": {}, "source": ["Nesta etapa, convertemos a coluna **`dt_pagto`** para o tipo `datetime` do pandas.  \n"]}, {"cell_type": "code", "execution_count": null, "id": "97fcbde2", "metadata": {}, "outputs": [], "source": ["dt[\"dt_pagto\"] = pd.to_datetime(dt[\"dt_pagto\"], format=\"%Y-%m-%d %H:%M:%S\", errors='coerce')"]}, {"cell_type": "code", "execution_count": null, "id": "a6bd7b86", "metadata": {}, "outputs": [], "source": ["dt.dtypes"]}, {"cell_type": "markdown", "id": "11bf93d7", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "7a39d63b", "metadata": {}, "source": ["# 2. Exploração dos dados"]}, {"cell_type": "markdown", "id": "9aa4cac6", "metadata": {}, "source": ["## 2.1. <PERSON><PERSON>lise Estatística Descritiva das Colunas Numéricas\n", "\n", "**Objetivo**: Calcular estatísticas descritivas completas para todas as colunas numéricas.\n", "\n", "**Processo**:\n", "1. Selecionamos apenas colunas numéricas\n", "2. Calculamos estatísticas básicas (count, mean, std, min, quartis, max)\n", "3. Adicionamos mediana e moda para análise mais robusta\n", "\n", "**Resultado**: Tabela com estatísticas descritivas completas para cada coluna numérica."]}, {"cell_type": "code", "execution_count": null, "id": "c31a5055", "metadata": {}, "outputs": [], "source": ["colunas_numericas = dt.select_dtypes(include=['number'])\n", "descricao = colunas_numericas.describe()\n", "\n", "\n", "median = pd.DataFrame({\n", "    'id_grupo': [colunas_numericas['id_grupo'].median()],\n", "    'id_beneficiario': [colunas_numericas['id_beneficiario'].median()],\n", "    'Numero_do_boleto': [colunas_numericas['Numero_do_boleto'].median()],\n", "    'vl_boleto': [colunas_numericas['vl_boleto'].median()],\n", "    'id_pagador': [colunas_numericas['id_pagador'].median()]\n", "})\n", "\n", "mode = pd.DataFrame({\n", "    'id_grupo': [colunas_numericas['id_grupo'].mode().iloc[0]],\n", "    'id_beneficiario': [colunas_numericas['id_beneficiario'].mode().iloc[0]],\n", "    'Numero_do_boleto': [colunas_numericas['Numero_do_boleto'].mode().iloc[0]],\n", "    'vl_boleto': [colunas_numericas['vl_boleto'].mode().iloc[0]],\n", "    'id_pagador': [colunas_numericas['id_pagador'].mode().iloc[0]]\n", "})\n", "\n", "descricao = pd.concat([descricao, median])\n", "descricao.rename(index={0: 'median'}, inplace=True)\n", "\n", "descricao = pd.concat([descricao, mode])\n", "descricao.rename(index={0: 'mode'}, inplace=True)\n", "\n", "\n", "display(descricao)"]}, {"cell_type": "markdown", "id": "451bfcfe", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "c1fbe68a", "metadata": {}, "source": ["## 2.2. Classificação das Colunas por Tipo de Dados \n", "\n", "**Objetivo**: Identificar e separar colunas numéricas e categóricas para análise adequada.\n", "\n", "**Processo**:\n", "1. <PERSON><PERSON><PERSON> `select_dtypes()` para separar colunas por tipo\n", "2. Criamos dataframes organizados mostrando a classificação\n", "3. Exibimos as listas separadas para verificação\n", "\n", "**Resultado**: <PERSON>as tabelas mostrando colunas numéricas e categóricas identificadas."]}, {"cell_type": "code", "execution_count": null, "id": "0dd32e2c", "metadata": {}, "outputs": [], "source": ["colunas_categoricas = dt.select_dtypes(include=['object', 'category'])\n", "\n", "dt_categoria = pd.DataFrame({\n", "    'Tipo': ['Categoricas'] * len(colunas_categoricas.columns),\n", "    'Coluna': colunas_categoricas.columns.tolist()\n", "    })\n", "\n", "dt_numericos = pd.DataFrame({\n", "    'Tipo': ['Numericas'] * len(colunas_numericas.columns),\n", "    'Coluna': colunas_numericas.columns.tolist()\n", "})\n", "\n", "display(dt_categoria)\n", "display(dt_numericos)"]}, {"cell_type": "markdown", "id": "186c5fdc", "metadata": {}, "source": ["--- "]}, {"cell_type": "markdown", "id": "b4bf6e6c", "metadata": {}, "source": ["## 2.3. Criação de gráficos para análise de dados\n", "\n", "**Objetivo**: Mostrar gráficos que expoem padrões de dados e outliers \n", "\n", "**Processo**:\n", "1. Selecionamos colunas especificas para colocar como eixo do gráfico\n", "2. Criamos os gráficos utilizando a biblioteca matplotlib\n", "3. Análisamos os gráficos e tiraremos conclusões a partir de como os dados estão distribuidos \n", "\n", "**Resultados**: Gráficos que organizão os dados com base em cada coluna e conclusões que foram baseadas nas análises dos gráficos"]}, {"cell_type": "markdown", "id": "0951a658", "metadata": {}, "source": ["#### **2.3.1 Gráfico 1** — Preço (R$) × Status do Boleto\n", "\n", "**Conclusão**: `O gráfico mostra que os valores dos boletos não necessáriamente estão ligados ao boleto ser liquidado`"]}, {"cell_type": "code", "execution_count": null, "id": "beb8659f", "metadata": {}, "outputs": [], "source": ["import plotly.express as px\n", "\n", "fig = px.scatter(\n", "    dt,\n", "    x=\"vl_boleto\",\n", "    y=\"status_boleto\",\n", "    title=\"Gráfico 1 - Preço X Status\",\n", "    labels={\n", "        \"vl_boleto\": \"Preço (R$)\",\n", "        \"status_boleto\": \"Status do Boleto\"\n", "    }\n", ")\n", "\n", "fig.show()\n"]}, {"cell_type": "markdown", "id": "ae3bb9ea", "metadata": {}, "source": ["#### **2.3.2 Gráfico 2** — Inadimplência × Período de Pagamento (dias)\n", "\n", "**Conclusão**: `Com o aumento dos períodos podemos ver um aumento na inadimplência em comparação aos não inadimplentes`"]}, {"cell_type": "code", "execution_count": null, "id": "deea9ec1", "metadata": {}, "outputs": [], "source": ["# converte para datetime\n", "dt[\"dt_pagto\"] = pd.to_datetime(dt[\"dt_pagto\"], errors=\"coerce\")\n", "dt[\"data_vencto\"] = pd.to_datetime(dt[\"data_vencto\"], errors=\"coerce\")\n", "\n", "#Criação da coluna que indica quem são os inadimplentes\n", "dt['inadimplente'] = (dt['dt_pagto'].isnull()) & (dt[\"status_boleto\"] != \"LIQUIDADO\") | ((dt['data_vencto'] < dt['dt_pagto']) & (dt['status_boleto'] != 'LIQUIDADO'))\n", "\n", "#Criação da coluna que mostra o perdiodo de pagamento do boleto em dias\n", "dt['periodo_de_pagamento'] = dt['data_vencto'] - dt['data_inclusao']\n", "dt['periodo_de_pagamento_dias'] = dt[\"periodo_de_pagamento\"].dt.days\n", "\n", "#Fazer com que a análise de inadimplencia seja \"inadimplente\" e \"não inadimplente\" e não so \"True\" e \"False\"\n", "dt['Inadimplencia'] = dt['inadimplente'].map({\n", "    True: 'Inadimplentes',\n", "    False: 'Adimplentes'\n", "})\n", "\n", "#Criação do Gráfico\n", "grafico = px.histogram(\n", "  dt, \n", "  x=\"periodo_de_pagamento_dias\", \n", "  color=\"Inadimplencia\", \n", "  barmode=\"group\", \n", "  labels={\n", "    'periodo_de_pagamento_dias': 'Periodo de pagamento (Dias)', \n", "    'count': 'Total de boletos'\n", "    }\n", ")\n", "grafico.update_layout(\n", "  yaxis_title='Total de boletos', \n", "  title='Gráfico 2 - Inadimplência X Periodo de pagamento'\n", "  )"]}, {"cell_type": "markdown", "id": "e2622411", "metadata": {}, "source": ["#### **2.3.3 Gráfico 3** — Acessos a<PERSON> Bo<PERSON>o × Status\n", "\n", "**Conclusão**: `Os acessos ao boleto não necessariamente favorece ou desfavorece a inadimplência dos clientes`"]}, {"cell_type": "code", "execution_count": null, "id": "2ba0649e", "metadata": {}, "outputs": [], "source": ["# substitui '\\N' por 0 antes de plotar\n", "dt[\"qtd_acessos_pagador\"] = dt[\"qtd_acessos_pagador\"].replace(r'\\N', 0)\n", "\n", "fig = px.scatter(\n", "    dt,\n", "    x=\"qtd_acessos_pagador\",\n", "    y=\"status_boleto\",\n", "    title=\"Gráfico 3 - Acessos ao boleto X Status do boleto\",\n", "    labels={\n", "        \"qtd_acessos_pagador\": \"Quantidade de acessos\",\n", "        \"status_boleto\": \"Status do boleto\"\n", "    }\n", ")\n", "\n", "fig.show()\n"]}, {"cell_type": "markdown", "id": "b497e928", "metadata": {}, "source": ["#### **2.3.4 Gráfico 4** — Taxa de Inadimplência (%) por Banco\n", "\n", "**Conclusão**: `O grupo de pagadores que utilizam dos serviços do Banco Citi e do Banco do Brasil, tem uma tendência de ser inadimplente`"]}, {"cell_type": "code", "execution_count": null, "id": "233128a2", "metadata": {}, "outputs": [], "source": ["\n", "# transforma True/False em 1/0\n", "dt[\"inadimplente_int\"] = dt[\"inadimplente\"].astype(int)\n", "\n", "# total e soma de inadimplentes por banco\n", "inadimplentes_por_banco = (\n", "    dt.groupby(\"banco\", as_index=False)\n", "    .agg(total=(\"inadimplente_int\", \"count\"),\n", "         inadimplentes=(\"inadimplente_int\", \"sum\"))\n", ")\n", "\n", "# calcula percentual\n", "inadimplentes_por_banco[\"perc_inadimplencia\"] = (\n", "    inadimplentes_por_banco[\"inadimplentes\"] / inadimplentes_por_banco[\"total\"] * 100\n", ")\n", "\n", "fig = px.scatter(\n", "    inadimplentes_por_banco,\n", "    x=\"banco\",\n", "    y=\"perc_inadimplencia\",\n", "    size=\"total\",\n", "    color=\"perc_inadimplencia\",\n", "    color_continuous_scale=\"RdYlGn_r\",\n", "    title=\"Taxa de Inadimplência (%) por Banco\",\n", "    labels={\n", "        \"perc_inadimplencia\": \"Inadimplência (%)\",\n", "        \"total\": \"Total de boletos\"\n", "    }\n", ")\n", "fig.show()\n", "\n"]}, {"cell_type": "markdown", "id": "995f5b3f", "metadata": {}, "source": ["# 3. Pré-Processamento dos Dados"]}, {"cell_type": "markdown", "id": "9d85e649", "metadata": {}, "source": ["### Objetivo e visão geral\n", "- Este notebook realiza a limpeza e a preparação dos dados do grupo GT para análises.\n", "- Foco: tratamento de valores ausentes, padronização de campos, identificação visual de outliers e normalização de variáveis numéricas.\n", "- Saídas esperadas: dataset mais consistente (tipos corretos, ausentes padronizados) e variáveis preparadas para modelagem e visualização.\n"]}, {"cell_type": "markdown", "id": "e89d4229", "metadata": {}, "source": ["## 3.1 Tratamento de Missing Values"]}, {"cell_type": "markdown", "id": "eda4da74", "metadata": {}, "source": ["Contando a quantidade de \\N e a quantidade de valores nulos antes do tratamento"]}, {"cell_type": "code", "execution_count": null, "id": "ab37ba46", "metadata": {}, "outputs": [], "source": ["(dt == r'\\N').sum()"]}, {"cell_type": "code", "execution_count": null, "id": "6631ae14", "metadata": {}, "outputs": [], "source": ["dt.isna().sum()\n"]}, {"cell_type": "markdown", "id": "c5cb151c", "metadata": {}, "source": ["##### `vl_pagto` (tratamento)\n", "- Preenchemos ausentes com `0` (não houve pagamento).\n", "- <PERSON>gu<PERSON>, convertemos para tipo numérico adequado.\n"]}, {"cell_type": "code", "execution_count": null, "id": "2dbcbb08", "metadata": {}, "outputs": [], "source": ["dt['vl_pagto'] = dt['vl_pagto'].fillna(0)"]}, {"cell_type": "markdown", "id": "d79db58f", "metadata": {}, "source": ["##### `qtd_acessos_pagador` (tratamento)\n", "- Preenchemos ausentes com `0` e convertimos para inteiro.\n", "- Interpretação: `0` indica nenhuma interação registrada pelo pagador.\n"]}, {"cell_type": "code", "execution_count": null, "id": "2e92dfd6", "metadata": {}, "outputs": [], "source": ["dt['qtd_acessos_pagador'] = dt['qtd_acessos_pagador'].fillna(0)"]}, {"cell_type": "markdown", "id": "45bf4ce6", "metadata": {}, "source": ["##### Substituímos todos os \"\\\\N\" (texto) por `NaN` para unificar a representação de ausentes."]}, {"cell_type": "code", "execution_count": null, "id": "29786756", "metadata": {}, "outputs": [], "source": ["dt = dt.replace(r'\\N', np.nan)"]}, {"cell_type": "code", "execution_count": null, "id": "82834245", "metadata": {}, "outputs": [], "source": ["(dt == r'\\N').sum()"]}, {"cell_type": "markdown", "id": "7dd23194", "metadata": {}, "source": ["##### `qtd_acessos_pagador` (tratamento)\n", "\n", "- preenchemos com 0 todos os valores nulos (NaN) da coluna 'qtd_acessos_pagador'.\n"]}, {"cell_type": "code", "execution_count": null, "id": "dd2aa5c4", "metadata": {}, "outputs": [], "source": ["dt['qtd_acessos_pagador'] = dt['qtd_acessos_pagador'].fillna(0)\n", "# falta passar pra inteiro"]}, {"cell_type": "markdown", "id": "136f78ec", "metadata": {}, "source": ["##### `pagador_cidade` (tratamento)\n", "- preenchemos com \"Não informado\" para preservar as linhas e manter uma categoria explícita."]}, {"cell_type": "code", "execution_count": null, "id": "03ff9d1b", "metadata": {}, "outputs": [], "source": ["dt['pagador_cidade'] = dt['pagador_cidade'].fillna('Não informado')\n"]}, {"cell_type": "markdown", "id": "aebbeb42", "metadata": {}, "source": ["##### `pagador_cep` (tratamento)\n", "- convertemos para numérico (`to_numeric`) para permitir validações e filtros; valores inválidos viram `NaN`."]}, {"cell_type": "code", "execution_count": null, "id": "af14979f", "metadata": {}, "outputs": [], "source": ["dt['pagador_cep'] = pd.to_numeric(dt['pagador_cep'], errors='coerce')"]}, {"cell_type": "markdown", "id": "91f967de", "metadata": {}, "source": ["##### `info()`\n", "- checamos os tipos finais e contagem de nulos para validar o resultado do tratamento.\n"]}, {"cell_type": "code", "execution_count": null, "id": "3188dd0a", "metadata": {}, "outputs": [], "source": ["display(dt.info())"]}, {"cell_type": "markdown", "id": "6a125324", "metadata": {}, "source": ["## 3.2. Identificação de `outliers`\n", "- Objetivo: inspecionar valores atípicos com boxplots para `vl_boleto`, `vl_pagto` e `qtd_acessos_pagador`.\n", "- Uso prático: orientar decisões de tratamento (ex.: capping/winsorização ou análise por segmento)."]}, {"cell_type": "markdown", "id": "6bed03a9", "metadata": {}, "source": ["####  Outliers — `vl_boleto`\n", "- Boxplot para inspecionar a distribuição e pontos fora do intervalo interquartil.\n", "- Observação: a ação corretiva não é automática nesta etapa."]}, {"cell_type": "code", "execution_count": null, "id": "a84de4c4", "metadata": {}, "outputs": [], "source": ["dt.plot.box(y='vl_boleto', figsize=(15, 5),grid=True)"]}, {"cell_type": "markdown", "id": "8be0dd3e", "metadata": {}, "source": ["#### Outliers — `vl_pagto`\n", "- Boxplot para inspecionar a distribuição de pagamentos e potenciais valores extremos."]}, {"cell_type": "code", "execution_count": null, "id": "6d16700e", "metadata": {}, "outputs": [], "source": ["dt.plot.box(y='vl_pagto', figsize=(15, 5),grid=True)"]}, {"cell_type": "markdown", "id": "3f6c56de", "metadata": {}, "source": ["#### Outliers — `qtd_acessos_pagador`\n", "- Boxplot para verificar assimetria e possíveis contagens atípicas.\n", "- Útil para orientar eventuais regras de capping ou análise por segmentos.\n"]}, {"cell_type": "code", "execution_count": null, "id": "28645594", "metadata": {}, "outputs": [], "source": ["dt.plot.box(y='qtd_acessos_pagador', figsize=(15, 5),grid=True)"]}, {"cell_type": "markdown", "id": "6d8ba245", "metadata": {}, "source": ["### Conclusão sobre os outliers\n", "\n", "Optamos por **não tratar os outliers neste momento**. A justificativa para essa escolha é dupla:\n", "\n", "1. **Avaliação no modelo**  \n", "   Inicialmente entendemos que seria mais adequado esperar a aplicação dos dados no modelo, de modo a observar qual impacto real os outliers poderiam gerar nas previsões. Dessa forma, conseguimos avaliar com mais clareza se eles prejudicam ou não o desempenho.\n", "\n", "2. **<PERSON><PERSON><PERSON> <PERSON> parceiro (Sprint Review 2)**  \n", "   Durante a Sprint Review 2, essa questão foi apresentada ao parceiro. Eles também não souberam determinar de antemão o peso desses outliers para o modelo, reforçando a decisão de esperar. O próprio parceiro destacou que seria necessário visualizar os resultados do modelo para opinar com maior segurança.\n", "\n", "👉 Assim, por ora, os outliers foram **identificados**, mas **não removidos ou transformados**, aguardando etapas posteriores de avaliação de impacto e alinhamento com o parceiro.\n"]}, {"cell_type": "markdown", "id": "1b018743", "metadata": {}, "source": ["## 3.3. Normalização das variáveis das `colunas quantitativas`"]}, {"cell_type": "markdown", "id": "595a5aff", "metadata": {}, "source": ["##### Normalização (<PERSON><PERSON><PERSON>)\n", "- <PERSON><PERSON><PERSON><PERSON><PERSON> `vl_boleto`, `vl_pagto` e `qtd_acessos_pagador` para o intervalo [0, 1].\n", "- Motivação: facilitar comparações e uso em modelos sensíveis à escala.\n", "- Observação: a normalização não remove outliers; apenas reescala a variável.\n"]}, {"cell_type": "code", "execution_count": null, "id": "6e9ff28f", "metadata": {}, "outputs": [], "source": ["dt['vl_boleto'] = (dt['vl_boleto'] - dt['vl_boleto'].min()) / (dt['vl_boleto'].max() - dt['vl_boleto'].min())\n", "dt['vl_pagto'] = (dt['vl_pagto'] - dt['vl_pagto'].min()) / (dt['vl_pagto'].max() - dt['vl_pagto'].min())\n", "dt['qtd_acessos_pagador'] = (dt['qtd_acessos_pagador'] - dt['qtd_acessos_pagador'].min()) / (dt['qtd_acessos_pagador'].max() - dt['qtd_acessos_pagador'].min())\n", "\n", "display(dt)\n"]}, {"cell_type": "markdown", "id": "5962d0ee", "metadata": {}, "source": ["## 3.4 Codificação correta das variáveis categóricas"]}, {"cell_type": "code", "execution_count": null, "id": "af14cb18", "metadata": {}, "outputs": [], "source": ["# Cria uma cópia do dataframe original\n", "df = dt.copy()"]}, {"cell_type": "markdown", "id": "195afeac", "metadata": {}, "source": ["Para realizar esse processo, transformare<PERSON> as variáveis \"pagador_cidade\", \"status_boleto\" e \"banco\", porque elas serão importantes para a análise da nossa predição."]}, {"cell_type": "markdown", "id": "8d1872d4", "metadata": {}, "source": ["Existem muitas linhas diferentes que se referem a mesma cidade, então, utilizaremos essa função para unificar elas:\n", "- Garante que valores nulos/NaN ou espaços vazios virem 'NAO INFORMADO';\n", "- <PERSON><PERSON><PERSON> acento<PERSON>;\n", "- Remove hífens e sufixos;\n", "- Corrige variações de uma mesma cidade;\n"]}, {"cell_type": "code", "execution_count": null, "id": "6915dc38", "metadata": {}, "outputs": [], "source": ["def limpar_cidade(cidade):\n", "    \n", "    if pd.isna(cidade):\n", "        return 'NAO INFORMADO'\n", "    \n", "    cidade = str(cidade).upper().strip() \n", "    \n", "    if cidade == '' or cidade in ['NAN', 'NA', 'NONE']:\n", "        return 'NAO INFORMADO'\n", "\n", "    cidade = unicodedata.normalize('NFKD', cidade).encode('ASCII', 'ignore').decode('utf-8')\n", "    \n", "    cidade = cidade.replace('-', '').replace('SP', '').strip()\n", "    \n", "    if 'SAO PAULO' in cidade or 'SCO PAULO' in cidade:\n", "        return 'SAO PAULO'\n", "    \n", "    return cidade\n", "\n", "df['pagador_cidade'] = df['pagador_cidade'].apply(limpar_cidade)\n"]}, {"cell_type": "markdown", "id": "ed467111", "metadata": {}, "source": ["<PERSON><PERSON><PERSON>, vamos converter a variável \"status_boleto\" para string, padronizar e mapear seus valores:"]}, {"cell_type": "code", "execution_count": null, "id": "9855906e", "metadata": {}, "outputs": [], "source": ["\n", "df['status_boleto_str'] = df['status_boleto'].astype(str).str.strip().str.upper()\n", "\n", "status_map = {\n", "    'LIQUIDADO': 0,\n", "    'BAIXADO': 1,\n", "    'REGISTRADO': 2 \n", "}\n", "df['status_boleto_final'] = df['status_boleto_str'].map(status_map)"]}, {"cell_type": "markdown", "id": "d56b5311", "metadata": {}, "source": ["<PERSON> seguida, codificaremos as variáveis \"pagador_cidade\" e \"banco\" como números inteiros:"]}, {"cell_type": "code", "execution_count": null, "id": "14920f70", "metadata": {}, "outputs": [], "source": ["df['status_boleto_str'] = df['status_boleto'].fillna('NAO INFORMADO').astype(str).str.strip().str.upper()\n", "\n", "numeric_to_status_map = {\n", "    '0.0': 'LIQUIDADO',\n", "    '1.0': 'BAIXADO',\n", "    '2.0': 'REGISTRADO'\n", "}\n", "df['status_boleto_str'] = df['status_boleto_str'].replace(numeric_to_status_map)\n", "\n", "status_map = {\n", "    'LIQUIDADO': 0,\n", "    'BAIXADO': 1,\n", "    'REGISTRADO': 2,\n", "    'NAO INFORMADO': 3\n", "}\n", "\n", "df['status_boleto_final'] = df['status_boleto_str'].map(status_map)\n", "\n", "df['pagador_cidade'] = df['pagador_cidade'].apply(limpar_cidade)\n", "\n", "df['pagador_cidade_code'] = df['pagador_cidade'].astype('category').cat.codes\n", "df['banco_code'] = df['banco'].astype('category').cat.codes\n", "\n", "display(df[['status_boleto_str', 'pagador_cidade', 'banco']])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}