import pandas as pd
import numpy as np
import glob
from IPython.display import display
from pathlib import Path
import re
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression

arquivos = glob.glob("../dados_novos/*.csv") # lista de caminhos para todos os arquivos .csv dentro da pasta ../dados/
dfs = [] # cria uma lista vazia
for arq in arquivos: # para cada caminho dentro de todos os caminhos para as tabelas dos dados:
    df = pd.read_csv(arq, sep="\t", encoding="utf-8-sig", engine="python") # leitura do arquivo
    nome_arquivo = Path(arq).name  # Ex: "Grupo com registro entre 07-2024 a 06-2025- GW.csv"
    grupo = re.search(r"- ([^-\s]+)\.csv$", nome_arquivo, re.IGNORECASE)
    sigla_grupo = grupo.group(1) if grupo else "Desconhecido"
    df["grupo"] = sigla_grupo
    dfs.append(df) # adiciona cada df a lista de dfs
dados = pd.concat(dfs, ignore_index=True) # concatena todos os dataframes da lista dfs em um único dataframe chamado dt
display(dados.head()) # mostra as primeiras 5 linhas
print(dados.shape) # imprime o numero de linhas e colunas do dt

# Imprime a lista de nomes das colunas do DataFrame
print(dados.columns.tolist())

# Define a função que converte um número de CEP na sigla do estado correspondente
def cep_para_estado(cep):
    # Retorna None (nulo) se o CEP for inválido ou uma das strings de placeholder
    if pd.isna(cep) or str(cep).strip() in ['\\N', 'nan', 'NÃO INFORMADO', 'NAO INFORMADO', 'NINF', 'nf', '']:
        return None

    # Converter para string e limpar
    cep_str = str(cep).strip().replace('-', '').replace('.', '').replace(' ', '')

    # Manter só dígitos
    cep_str = ''.join([c for c in cep_str if c.isdigit()])

    # Se tiver menos de 5 dígitos, inválido
    if len(cep_str) < 5:
        return None

    # Completar com zeros à esquerda até 8 dígitos
    cep_str = cep_str.zfill(8)  # '2809040' → '02809040'

    # Pegar os 2 primeiros dígitos
    dois_digitos = cep_str[:2]

    # Mapeamento por 2 dígitos (como antes)
    mapeamento = {
        '01': 'SP', '02': 'SP', '03': 'SP', '04': 'SP', '05': 'SP', '06': 'SP', '07': 'SP', '08': 'SP', '09': 'SP',
        '10': 'SP', '11': 'SP', '12': 'SP', '13': 'SP', '14': 'SP', '15': 'SP', '16': 'SP', '17': 'SP', '18': 'SP', '19': 'SP',
        '20': 'RJ', '21': 'RJ', '22': 'RJ', '23': 'RJ', '24': 'RJ', '25': 'RJ', '26': 'RJ', '27': 'RJ', '28': 'RJ',
        '29': 'ES',
        '30': 'MG', '31': 'MG', '32': 'MG', '33': 'MG', '34': 'MG', '35': 'MG', '36': 'MG', '37': 'MG', '38': 'MG', '39': 'MG',
        '40': 'BA', '41': 'BA', '42': 'BA', '43': 'BA', '44': 'BA', '45': 'BA', '46': 'BA', '47': 'BA', '48': 'SE', '49': 'SE',
        '50': 'PE', '51': 'PE', '52': 'PE', '53': 'PE', '54': 'PE', '55': 'PE', '56': 'PE', '57': 'AL', '58': 'PB', '59': 'RN',
        '60': 'CE', '61': 'CE', '62': 'CE', '63': 'CE', '64': 'PI', '65': 'MA', '66': 'PA', '67': 'PA', '68': 'AP', '69': 'AM',
        '70': 'DF', '71': 'DF', '72': 'DF', '73': 'GO', '74': 'GO', '75': 'GO', '76': 'TO', '77': 'TO', '78': 'MT', '79': 'MS',
        '80': 'PR', '81': 'PR', '82': 'PR', '83': 'PR', '84': 'PR', '85': 'PR', '86': 'PR', '87': 'PR', '88': 'SC', '89': 'SC',
        '90': 'RS', '91': 'RS', '92': 'RS', '93': 'RS', '94': 'RS', '95': 'RS', '96': 'RS', '97': 'RS', '98': 'RS', '99': 'RS'
    }

    # Retorna a sigla do estado do dicionário; se não encontrar, retorna None
    return mapeamento.get(dois_digitos, None)

# Aplica a função 'cep_para_estado' a cada valor da coluna 'pagador_cep' para criar a nova coluna
dados['pagador_estado'] = dados['pagador_cep'].apply(cep_para_estado)

# Substitui os valores nulos (None/NaN) resultantes por uma string padrão
dados['pagador_estado'] = dados['pagador_estado'].fillna('Estado não informado')

# Conta e exibe a frequência de cada estado na nova coluna para verificar o resultado
print(dados['pagador_estado'].value_counts(dropna=False))

# Define uma lista com os nomes das colunas a serem removidas
colunas_para_remover = ['id_grupo', 'id_beneficiario', 'Numero_do_boleto', 'id_pagador', 'pagador_cidade', 'pagador_dt_ultimo_acesso', 'valor_abatimento', 'pagador_cep']
colunas_existentes = [col for col in colunas_para_remover if col in dados.columns]

# Remove as colunas especificadas do DataFrame
dados = dados.drop(columns=colunas_existentes)
dados.head()

# 1. Quantos missing values por coluna?
print("Valores faltantes por coluna:")
print(dados.isnull().sum())

# 3. Há alguma linha completamente vazia?
print(f"\nLinhas completamente vazias: {dados.isnull().all(axis=1).sum()}")

# Substitui '\N' por NaN em todo o DataFrame
dados = dados.replace(r'^\\N$', np.nan, regex=True)

# Exibe novamente a contagem de valores nulos para confirmar que a substituição funcionou
print("Valores faltantes por coluna:")
print(dados.isnull().sum())

# Exibe novamente a contagem de valores nulos para confirmar que a substituição funcionou
dados["qtd_acessos_pagador"] = dados["qtd_acessos_pagador"].fillna(0)

# Preenche os valores nulos da coluna 'tipo_multa' com a string "Não há multa"
dados["tipo_multa"] = dados["tipo_multa"].fillna("Não há multa")

# Preenche os valores nulos da coluna 'multa' com 0
dados["multa"] = dados["multa"].fillna(0)

display(dados)

# Exibe a contagem final de nulos para garantir que as colunas foram preenchidas
print("Valores faltantes por coluna:")
print(dados.isnull().sum())

# Dicionário para armazenar o mapeamento de cada coluna codificada
mapa_mapeamentos = {}

# Lista de colunas categóricas que serão transformadas em números
colunas_para_codificar = [
    "grupo",
    "status_boleto",
    "banco",
    "pagador_estado",
    "pagador_cnpjcpf",
    "tipo_juros",
    "tipo_multa",
]

print("Mapeamento: Código → Valor Original\n")

# Itera sobre cada coluna a ser codificada
for col in colunas_para_codificar:
    # Converte para categórico para garantir tratamento correto
    cat = pd.Categorical(dados[col])
    
    # Cria mapeamento: código (começando em 1) → valor original
    mapeamento = {i + 1: categoria for i, categoria in enumerate(cat.categories)}
    
    # Salva no dicionário geral
    mapa_mapeamentos[col] = mapeamento
    
    # Aplica o encoding no DataFrame
    dados[col] = cat.codes + 1  # +1 para começar de 1, não de 0
    
    # Exibi mapeamento
    print(f"🔹 Coluna: '{col}'")
    for codigo, valor in sorted(mapeamento.items()):
        print(f"   {codigo} → {valor}")
    print("-" * 50)

# Imprime os tipos de dados (dtypes) de cada coluna do DataFrame
print(dados.dtypes)

# Lista de colunas que devem ser do tipo data
colunas_data = ['data_inclusao', 'data_vencto', 'dt_pagto']

for col in colunas_data:
    dados[col] = pd.to_datetime(dados[col], errors='coerce')
    # errors='coerce' → transforma valores inválidos em NaT (Not a Time)

# Converte colunas que deveriam ser numéricas, mas estão como texto ('object')
# 'errors=coerce' transforma textos que não são números em NaN (Not a Number)
dados["vl_pagto"] = pd.to_numeric(dados["vl_pagto"], errors='coerce')
dados["multa"] = pd.to_numeric(dados["multa"], errors='coerce')
dados["qtd_acessos_pagador"] = pd.to_numeric(dados["qtd_acessos_pagador"], errors='coerce')
dados["pagador_estado"] = pd.to_numeric(dados["pagador_estado"], errors='coerce')


# Converter para int ou float
dados["vl_pagto"] = dados["vl_pagto"].astype('float64')
dados["multa"] = dados["multa"].astype('float64')
dados["qtd_acessos_pagador"] = dados["qtd_acessos_pagador"].astype('int64')
dados["pagador_estado"] = dados["pagador_estado"].astype('int64')

# Imprime os tipos de dados novamente para confirmar as conversões
print(dados.dtypes)

# Calcula a diferença entre a data de vencimento e a data de inclusão e extrai o número de dias
dados['dias_para_vencimento'] = (dados['data_vencto'] - dados['data_inclusao']).dt.days

# Verificar valores negativos (vencimento antes da inclusão)
negativos = dados[dados['dias_para_vencimento'] < 0]
if len(negativos) > 0:
    print(f"\n⚠️  {len(negativos)} boletos têm vencimento ANTES da inclusão (erro?)")
    print(negativos[['data_inclusao', 'data_vencto', 'dias_para_vencimento']].head())

# Remover linhas onde o vencimento é antes da inclusão
dados = dados[dados['dias_para_vencimento'] >= 0].copy()

# Resetar o índice (opcional, mas recomendado)
dados.reset_index(drop=True, inplace=True)

print(f"✅ Dados com erro removidos. Novo total de linhas: {len(dados)}")

# Separa linhas onde dt_pagto é NaN (boletos não pagos) → "em aberto"
dados_em_aberto = dados[dados["dt_pagto"].isnull()].copy()
dados_pagos = dados[dados["dt_pagto"].notnull()].copy()

# Imprime as contagens para verificar se a separação foi feita corretamente
print(f"Total de registros: {len(dados)}")
print(f"Em aberto (sem data de pagamento): {len(dados_em_aberto)}")
print(f"Pagos (com data de pagamento): {len(dados_pagos)}")

# Verifica a quantidade de valores nulos em cada um dos novos DataFrames
print("Valores faltantes dados_em_aberto:")
print(dados_em_aberto.isnull().sum())
print("Valores faltantes dados_pagos:")
print(dados_pagos.isnull().sum())

# Filtrar a linha com data_vencto e dias_para_vencimento faltante
linha_faltante = dados_pagos[dados_pagos['data_vencto'].isna()]
# Remove do DataFrame 'dados_pagos' as linhas onde a 'data_vencto' e 'dias_para_vencimento' é nula
dados_pagos = dados_pagos.dropna(subset=['data_vencto']).reset_index(drop=True)

# Exibe a linha que foi removida para inspeção
print("Linha com data_vencto faltante:")
print(linha_faltante[['data_inclusao', 'vl_boleto', 'banco', 'grupo', 'pagador_estado', 'data_vencto', 'dias_para_vencimento']])

# Re-verifica a contagem de nulos para confirmar a remoção
print("Valores faltantes dados_em_aberto:")
print(dados_em_aberto.isnull().sum())
print("Valores faltantes dados_pagos:")
print(dados_pagos.isnull().sum())

# Calcula a diferença em dias entre a data de pagamento e a data de vencimento
diferenca_dias = (dados_pagos['dt_pagto'] - dados_pagos['data_vencto']).dt.days

# Define as condições para classificar o status do pagamento
condicoes = [
    # 1. Pago antes do vencimento
    (diferenca_dias < 0),
    # 2. Pago até 30 dias após o vencimento (em dia/almoço)
    (diferenca_dias >= 0) & (diferenca_dias <= 30),
    # 3. Pago com mais de 30 dias de atraso (inadimplente)
    (diferenca_dias > 30)
]

valores = [
    'Pago antes do vencimento',
    'Pago com até 30 dias de atraso',
    'Inadimplente (atraso > 30 dias)'
]

# Cria a nova coluna 'situacao_pagamento' aplicando as condições e valores
dados_pagos['situacao_pagamento'] = np.select(condicoes, valores, default='Erro')

# Exibe a contagem de cada categoria na nova coluna
print(dados_pagos['situacao_pagamento'].value_counts(dropna=False))

# Cria um dicionário para mapear as descrições da situação para códigos numéricos
mapeamento_situacao = {
    'Pago antes do vencimento': 1,
    'Pago com até 30 dias de atraso': 2,
    'Inadimplente (atraso > 30 dias)': 3
}

# Aplica o mapeamento para converter a coluna de texto em números
dados_pagos['situacao_pagamento'] = dados_pagos['situacao_pagamento'].map(mapeamento_situacao)

print("Mapeamento da situação de pagamento:")
for descricao, codigo in mapeamento_situacao.items():
    print(f"   {codigo} → {descricao}")


# Exibe o DataFrame final de boletos pagos, agora com as novas features
display(dados_pagos)

# Exibe o DataFrame final de boletos em aberto
display(dados_em_aberto)

# Calcula a variável alvo (dias de atraso)
dias_de_atraso = (dados_pagos['dt_pagto'] - dados_pagos['data_vencto']).dt.days

# A função describe() revela estatísticas essenciais da nossa variável alvo.
print(dias_de_atraso.describe())

# Cria a coluna 'dias_de_atraso' para facilitar a filtragem
dados_pagos['dias_de_atraso'] = (dados_pagos['dt_pagto'] - dados_pagos['data_vencto']).dt.days

# Filtramos por um valor negativo e irrealista (ex: pagamento > 2 anos adiantado) para visualizar os casos mais extremos.
erros_claros = dados_pagos[dados_pagos['dias_de_atraso'] < -730]

# Exibe as colunas de data para entender o que aconteceu
display(erros_claros[['data_inclusao', 'data_vencto', 'dt_pagto', 'dias_de_atraso', 'vl_boleto']])

# --- INÍCIO DA LIMPEZA DOS DADOS ---

print(f"Tamanho original de dados_pagos: {len(dados_pagos)} linhas")

# PASSO 1: Remover erros de sistema (datas de pagamento muito antigas)
# Filtra para manter apenas os pagamentos feitos após o ano 2000
dados_pagos_limpos = dados_pagos[dados_pagos['dt_pagto'].dt.year >= 2000].copy()
print(f"Após remover datas inválidas: {len(dados_pagos_limpos)} linhas")

# PASSO 2: Recalcula dias de atraso e remover quitações antecipadas
dados_pagos_limpos['dias_de_atraso'] = (dados_pagos_limpos['dt_pagto'] - dados_pagos_limpos['data_vencto']).dt.days

# Removemos pagamentos feitos com mais de 90 dias de antecedência.
# Esta regra de negócio remove as quitações de contrato, que não são o foco do nosso modelo de previsão de atraso.
dados_pagos_final = dados_pagos_limpos[dados_pagos_limpos['dias_de_atraso'] > -90].copy()

print(f"Após remover quitações antecipadas: {len(dados_pagos_final)} linhas")

# --- FIM DA LIMPEZA ---

# Verificamos as estatísticas do novo DataFrame para confirmar que a limpeza foi bem-sucedida.
print("\nNovas estatísticas para 'dias_de_atraso':")
print(dados_pagos_final['dias_de_atraso'].describe())

# 10. Importação das Bibliotecas para o Modelo
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.model_selection import train_test_split
import pandas as pd
import numpy as np

# (O código anterior de carregamento, pré-processamento e LIMPEZA dos dados viria aqui)
# Assumindo que 'dados_pagos_final' é o DataFrame limpo e pronto para o modelo.

# 11. Preparação Final dos Dados para o Modelo (COM DADOS LIMPOS)

### MUDANÇA ###
# Usamos a coluna 'dias_de_atraso' que já foi calculada e limpa.
y = dados_pagos_final['dias_de_atraso']

### MUDANÇA ###
# As features (X) são as colunas do dataframe limpo.
# Removemos as datas e a própria variável alvo ('dias_de_atraso') para evitar vazamento de dados.
X = dados_pagos_final.drop(columns=['dt_pagto', 'data_inclusao', 'data_vencto', 'dias_de_atraso', 'situacao_pagamento'])

# Verifica se todas as colunas em X são numéricas
non_numeric_cols = X.select_dtypes(include=['object', 'datetime64[ns]']).columns
if len(non_numeric_cols) > 0:
    print(f"Atenção: Removendo colunas não-numéricas de X: {non_numeric_cols.tolist()}")
    X = X.drop(columns=non_numeric_cols)

# 12. Divisão dos Dados em Treino e Teste
# Dividimos os dados para treinar o modelo e depois testar sua performance em dados que ele nunca viu.
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

print(f"Tamanho do conjunto de treino: {X_train.shape[0]} linhas")
print(f"Tamanho do conjunto de teste: {X_test.shape[0]} linhas")

# 13. Criação e Treinamento do Modelo Gradient Boosting

# Instancia o modelo. Os parâmetros podem ser mantidos.
gbr = GradientBoostingRegressor(
    n_estimators=100,
    learning_rate=0.1,
    max_depth=3,
    random_state=42,
    loss='squared_error'
)

# Treina o modelo com os dados de treino limpos
print("\nIniciando o treinamento do modelo com dados limpos...")
gbr.fit(X_train, y_train)
print("Treinamento concluído!")

# 14. Realizando Previsões no Conjunto de Teste
y_pred = gbr.predict(X_test)

# 15. Avaliação da Performance do Modelo

# Calcula as métricas de erro
mae = mean_absolute_error(y_test, y_pred)
rmse = np.sqrt(mean_squared_error(y_test, y_pred))
r2 = r2_score(y_test, y_pred)

print("\n--- Métricas de Avaliação do Modelo (DADOS LIMPOS) ---")
print(f"Erro Médio Absoluto (MAE): {mae:.2f} dias")
print(f"Raiz do Erro Quadrático Médio (RMSE): {rmse:.2f} dias")
print(f"Coeficiente de Determinação (R²): {r2:.2f}")
print("------------------------------------------------------")
print("\nExplicação das métricas:")
print(f"  - MAE: Em média, as previsões do modelo erram por aproximadamente {mae:.2f} dias.")
print(f"  - R²: O modelo explica aproximadamente {r2*100:.1f}% da variabilidade nos dias para o pagamento.")


# 16. Exemplo de Previsão e Conversão para Data

# Pega a primeira linha dos dados de teste para um exemplo prático
exemplo_teste = X_test.iloc[[0]]
dias_previstos = gbr.predict(exemplo_teste)[0]

### MUDANÇA ###
# Pega a data de vencimento correspondente a essa linha do dataframe limpo.
data_vencimento_real = dados_pagos_final.loc[exemplo_teste.index, 'data_vencto'].iloc[0]

# Calcula a data de pagamento prevista
data_pagamento_prevista = data_vencimento_real + pd.to_timedelta(dias_previstos, unit='D')

### MUDANÇA ###
# Pega a data de pagamento real para comparação do dataframe limpo.
data_pagamento_real = dados_pagos_final.loc[exemplo_teste.index, 'dt_pagto'].iloc[0]

print("\n--- Exemplo de Previsão (com dados limpos) ---")
print(f"Data de Vencimento: {data_vencimento_real.strftime('%Y-%m-%d')}")
print(f"Dias até o pagamento (Previsto pelo modelo): {dias_previstos:.1f} dias")
print(f"Data de Pagamento Prevista: {data_pagamento_prevista.strftime('%Y-%m-%d')}")
print(f"Data de Pagamento Real: {data_pagamento_real.strftime('%Y-%m-%d')}")
print("------------------------------------------------\n")

import pandas as pd
import numpy as np
from sklearn.preprocessing import RobustScaler, LabelEncoder
from datetime import datetime

def criar_features_avancadas(df):
    """Cria features mais sofisticadas para melhorar o modelo"""
    
    # 1. FEATURES TEMPORAIS AVANÇADAS
    df['data_vencimento'] = pd.to_datetime(df['data_vencimento'])
    df['data_pagamento'] = pd.to_datetime(df['data_pagamento'])
    
    # Dia da semana do vencimento (comportamento sazonal)
    df['dia_semana_vencimento'] = df['data_vencimento'].dt.dayofweek
    df['fim_semana_vencimento'] = (df['dia_semana_vencimento'] >= 5).astype(int)
    
    # Mês do vencimento (sazonalidade anual)
    df['mes_vencimento'] = df['data_vencimento'].dt.month
    df['trimestre_vencimento'] = df['data_vencimento'].dt.quarter
    
    # 2. FEATURES FINANCEIRAS SOFISTICADAS
    # Razão valor pago / valor boleto
    df['razao_pagamento'] = df['valor_pago'] / (df['valor boleto'] + 1e-8)
    
    # Diferença absoluta e percentual
    df['diferenca_valor'] = df['valor_pago'] - df['valor boleto']
    df['diferenca_percentual'] = df['diferenca_valor'] / (df['valor boleto'] + 1e-8)
    
    # Categorização de valores
    df['faixa_valor'] = pd.cut(df['valor boleto'], 
                            bins=[0, 100, 500, 1000, 5000, np.inf],
                            labels=['muito_baixo', 'baixo', 'medio', 'alto', 'muito_alto'])
    
    # 3. FEATURES COMPORTAMENTAIS
    # Histórico por pagador
    df['historico_atrasos'] = df.groupby('pagador_documento')['dias_de_atraso'].transform('mean')
    df['desvio_atraso_pagador'] = df.groupby('pagador_documento')['dias_de_atraso'].transform('std').fillna(0)
    df['count_boletos_pagador'] = df.groupby('pagador_documento').cumcount() + 1
    
    # Padrão de acesso
    df['acesso_por_valor'] = df['qtde_acessado_pagador'] / (df['valor boleto'] + 1e-8)
    df['alta_interacao'] = (df['qtde_acessado_pagador'] > df['qtde_acessado_pagador'].quantile(0.75)).astype(int)
    
    # 4. FEATURES GEOGRÁFICAS
    # Agrupamento por região
    df['regiao_risco'] = df.groupby('pagador_cidade')['dias_de_atraso'].transform('mean')
    df['cidade_volume'] = df.groupby('pagador_cidade').cumcount() + 1
    
    # 5. FEATURES DE INTERAÇÃO
    df['valor_x_acesso'] = df['valor boleto'] * df['qtde_acessado_pagador']
    df['valor_x_historico'] = df['valor boleto'] * df['historico_atrasos']
    
    return df

# Aplicar feature engineering
dados_melhorados = criar_features_avancadas(dados_pagos_final.copy())